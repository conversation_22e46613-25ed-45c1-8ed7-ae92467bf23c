using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using SmartVolumeStrategy.Core.Interfaces;
using SmartVolumeStrategy.Core.Models;
using ATAS.Indicators;
using ATAS.DataFeedsCore;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading.Channels;
using TradeDirection = SmartVolumeStrategy.Core.Models.TradeDirection;

namespace SmartVolumeStrategy.ATAS.Platform;

/// <summary>
/// High-performance bridge for converting ATAS market data to Core system format
/// Phase 2 Optimizations: Object pooling, lock-free performance tracking, ATAS profiling integration
/// Target: less than 2ms conversion time with under 20ms total processing
/// </summary>
public class ATASMarketDataBridge : IDisposable
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly ILogger<ATASMarketDataBridge> _logger;
    private readonly ObjectPool<Dictionary<string, object>> _dictionaryPool;
    private readonly Channel<MarketDataArg> _processingChannel;
    private readonly ChannelWriter<MarketDataArg> _channelWriter;

    // High-resolution performance tracking (lock-free)
    private long _totalConversions;
    private long _totalConversionTimeTicks;
    private long _slowConversions;
    private readonly long _ticksPerMs = Stopwatch.Frequency / 1000;

    // ATAS-native performance integration
    private readonly BaseIndicator? _parentIndicator;

    /// <summary>
    /// Initializes the high-performance ATAS market data bridge
    /// </summary>
    public ATASMarketDataBridge(
        ISignalGenerator signalGenerator,
        ILogger<ATASMarketDataBridge> logger,
        ObjectPool<Dictionary<string, object>>? dictionaryPool = null,
        BaseIndicator? parentIndicator = null)
    {
        _signalGenerator = signalGenerator ?? throw new ArgumentNullException(nameof(signalGenerator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _parentIndicator = parentIndicator;

        // Initialize object pool for dictionary reuse
        _dictionaryPool = dictionaryPool ?? CreateDefaultDictionaryPool();

        // Initialize high-performance async processing channel
        var channelOptions = new BoundedChannelOptions(1000)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = true,
            SingleWriter = false
        };
        _processingChannel = Channel.CreateBounded<MarketDataArg>(channelOptions);
        _channelWriter = _processingChannel.Writer;

        // Start background processing task
        _ = Task.Run(ProcessChannelDataAsync);
    }

    /// <summary>
    /// Creates default dictionary pool for object reuse
    /// </summary>
    private static ObjectPool<Dictionary<string, object>> CreateDefaultDictionaryPool()
    {
        var provider = new DefaultObjectPoolProvider();
        return provider.Create(new DictionaryPooledObjectPolicy());
    }

    /// <summary>
    /// Converts ATAS MarketDataArg to Core MarketData with Phase 2 optimizations
    /// Target: less than 1ms conversion time with object pooling and high-resolution timing
    /// </summary>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public MarketData ConvertToCore(MarketDataArg atasData)
    {
        if (atasData == null)
            throw new ArgumentNullException(nameof(atasData));

        var startTicks = Stopwatch.GetTimestamp();

        try
        {
            // ATAS-native performance measurement (if available)
            // Note: MeasurePerformance is protected, so we use our own high-resolution timing
            // In production ATAS environment, this would be replaced with proper ATAS profiling

            // PERFORMANCE FIX: Optimized bid/ask extraction with fallback logic
            var isBid = atasData.IsBid;
            var isAsk = atasData.IsAsk;
            var price = atasData.Price;
            var bid = isBid ? price : price - 0.00001m;
            var ask = isAsk ? price : price + 0.00001m;

            // Get pooled dictionary for additional data
            var additionalData = _dictionaryPool.Get();
            try
            {
                // PERFORMANCE FIX: Minimal additional data to reduce allocation overhead
                additionalData["IsBid"] = isBid;
                additionalData["IsAsk"] = isAsk;
                additionalData["Price"] = price;
                additionalData["Volume"] = atasData.Volume;

                // Create MarketData with optimized conversion
                var marketData = new MarketData
                {
                    Symbol = "UNKNOWN", // Will be set by caller if available
                    Timestamp = atasData.Time,
                    Price = atasData.Price,
                    Volume = (long)atasData.Volume,
                    Bid = bid,
                    Ask = ask,
                    BidVolume = 0, // Will be calculated separately
                    AskVolume = 0, // Will be calculated separately
                    Direction = ConvertTradeDirectionOptimized(atasData.Direction),
                    DataType = ConvertMarketDataTypeOptimized(isBid, isAsk),
                    QualityScore = 0.95, // High quality for real ATAS data
                    AdditionalData = new Dictionary<string, object>(additionalData)
                };

                var endTicks = Stopwatch.GetTimestamp();
                TrackPerformanceOptimized(startTicks, endTicks);

                return marketData;
            }
            finally
            {
                // Return dictionary to pool
                additionalData.Clear();
                _dictionaryPool.Return(additionalData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting ATAS MarketDataArg to Core MarketData");
            throw;
        }
    }

    /// <summary>
    /// Processes real-time ATAS data using high-performance channel pipeline
    /// Phase 2 Target: less than 1ms queuing time, under 20ms total processing
    /// </summary>
    public void ProcessRealTimeData(MarketDataArg atasData)
    {
        if (atasData == null)
        {
            _logger.LogWarning("Received null ATAS market data");
            return;
        }

        try
        {
            // High-performance queuing using channels (non-blocking)
            var startTicks = Stopwatch.GetTimestamp();

            if (_channelWriter.TryWrite(atasData))
            {
                var endTicks = Stopwatch.GetTimestamp();
                var queuingTimeMs = (endTicks - startTicks) / _ticksPerMs;

                // Log if queuing takes too long
                if (queuingTimeMs > 1.0)
                {
                    _logger.LogWarning("Data queuing took {QueuingTime:F2}ms (target: <1ms)", queuingTimeMs);
                }
            }
            else
            {
                _logger.LogWarning("Channel full - dropping market data tick");
                // Could implement overflow handling here
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queuing real-time ATAS data: Price={Price}, Volume={Volume}, Time={Time}",
                atasData.Price, atasData.Volume, atasData.Time);
        }
    }

    /// <summary>
    /// Processes market depth changes with enhanced order flow analysis
    /// </summary>
    public void ProcessMarketDepthChange(MarketDataArg depthData)
    {
        if (depthData == null) return;

        try
        {
            // Note: BaseIndicator.MeasurePerformance is not accessible from here
            // We'll use our own performance tracking
            var startTime = DateTime.UtcNow;

            var coreData = ConvertToCore(depthData);

            // Enhanced processing for depth data
            if (depthData.IsBid || depthData.IsAsk)
            {
                ProcessOrderBookUpdate(coreData, depthData);
            }

            var processingTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            if (processingTime > 5.0)
            {
                _logger.LogWarning("Market depth processing took {ProcessingTime:F2}ms (target: <5ms)", processingTime);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing market depth change");
        }
    }

    /// <summary>
    /// Processes order book updates for enhanced market analysis
    /// </summary>
    private void ProcessOrderBookUpdate(MarketData coreData, MarketDataArg atasData)
    {
        // Enhanced order flow analysis can be added here
        // This is where we can integrate with the adaptive system for order flow insights

        if (_signalGenerator is IOrderFlowAnalyzer orderFlowAnalyzer)
        {
            var orderFlowData = new OrderFlowData
            {
                Price = atasData.Price,
                Volume = (long)atasData.Volume,
                Direction = ConvertTradeDirection(atasData.Direction),
                Timestamp = atasData.Time,
                IsAggressive = atasData.AggressorExchangeOrderId.HasValue
            };

            orderFlowAnalyzer.ProcessOrderFlow(orderFlowData);
        }
    }

    /// <summary>
    /// PERFORMANCE FIX: Ultra-fast TradeDirection conversion with minimal allocations
    /// </summary>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private static SmartVolumeStrategy.Core.Models.TradeDirection ConvertTradeDirectionOptimized(object direction)
    {
        // PERFORMANCE FIX: Avoid ToString() allocation by checking object type first
        if (direction == null) return SmartVolumeStrategy.Core.Models.TradeDirection.Unknown;

        // Fast path: Check for common string values without allocation
        if (ReferenceEquals(direction, "Buy") || (direction is string str && str == "Buy"))
            return SmartVolumeStrategy.Core.Models.TradeDirection.Buy;
        if (ReferenceEquals(direction, "Sell") || (direction is string str2 && str2 == "Sell"))
            return SmartVolumeStrategy.Core.Models.TradeDirection.Sell;

        // Fallback for other cases
        var directionStr = direction.ToString();
        return directionStr switch
        {
            "Buy" => SmartVolumeStrategy.Core.Models.TradeDirection.Buy,
            "Sell" => SmartVolumeStrategy.Core.Models.TradeDirection.Sell,
            _ => SmartVolumeStrategy.Core.Models.TradeDirection.Unknown
        };
    }

    /// <summary>
    /// Optimized MarketDataType conversion using boolean flags
    /// Phase 2: Eliminates property access overhead
    /// </summary>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private static SmartVolumeStrategy.Core.Models.MarketDataType ConvertMarketDataTypeOptimized(bool isBid, bool isAsk)
    {
        return (isBid || isAsk)
            ? SmartVolumeStrategy.Core.Models.MarketDataType.Quote
            : SmartVolumeStrategy.Core.Models.MarketDataType.Trade;
    }

    /// <summary>
    /// Legacy conversion method for backward compatibility
    /// </summary>
    private static SmartVolumeStrategy.Core.Models.TradeDirection ConvertTradeDirection(object direction)
        => ConvertTradeDirectionOptimized(direction);

    /// <summary>
    /// Legacy conversion method for backward compatibility
    /// </summary>
    private static SmartVolumeStrategy.Core.Models.MarketDataType ConvertMarketDataType(MarketDataArg atasData)
        => ConvertMarketDataTypeOptimized(atasData.IsBid, atasData.IsAsk);

    /// <summary>
    /// High-resolution performance tracking using lock-free operations
    /// Phase 2 optimization: Eliminates lock contention
    /// </summary>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private void TrackPerformanceOptimized(long startTicks, long endTicks)
    {
        var elapsedTicks = endTicks - startTicks;
        var elapsedMs = elapsedTicks / _ticksPerMs;

        // Lock-free atomic operations
        Interlocked.Increment(ref _totalConversions);
        Interlocked.Add(ref _totalConversionTimeTicks, elapsedTicks);

        // Track slow conversions with enhanced threshold
        if (elapsedMs > 2) // Phase 2 target: <2ms
        {
            Interlocked.Increment(ref _slowConversions);

            // CRITICAL FIX: Only log warnings for significantly slow conversions to reduce noise
            if (elapsedMs > 5) // Only warn for conversions > 5ms (significantly slow)
            {
                _logger.LogWarning("⚠️ Market data conversion took {ElapsedMs:F2}ms (target: less than 2ms)", elapsedMs);
            }
            else if (_totalConversions % 1000 == 0) // Periodic summary for moderate slowdowns
            {
                var avgMs = (double)_totalConversionTimeTicks / _totalConversions / _ticksPerMs;
                _logger.LogInformation("📊 Market data conversion performance: Avg {AvgMs:F2}ms, Slow conversions: {SlowCount}/{TotalCount}",
                    avgMs, _slowConversions, _totalConversions);
            }
        }
    }

    /// <summary>
    /// Legacy performance tracking for backward compatibility
    /// </summary>
    private void TrackPerformance(long elapsedMs)
    {
        var elapsedTicks = elapsedMs * _ticksPerMs;
        TrackPerformanceOptimized(0, elapsedTicks);
    }

    /// <summary>
    /// Gets performance statistics with high-resolution timing
    /// </summary>
    public (long TotalConversions, double AverageTimeMs, long TotalTimeMs, long SlowConversions) GetPerformanceStats()
    {
        var totalConversions = Interlocked.Read(ref _totalConversions);
        var totalTimeTicks = Interlocked.Read(ref _totalConversionTimeTicks);
        var slowConversions = Interlocked.Read(ref _slowConversions);

        var totalTimeMs = totalTimeTicks / _ticksPerMs;
        var avgTimeMs = totalConversions > 0 ? (double)totalTimeMs / totalConversions : 0;

        return (totalConversions, avgTimeMs, totalTimeMs, slowConversions);
    }

    /// <summary>
    /// Disposes the bridge and reports final performance statistics
    /// </summary>
    public void Dispose()
    {
        // Close the processing channel
        _channelWriter.Complete();

        var (total, avg, totalTime, slow) = GetPerformanceStats();
        _logger.LogInformation("ATASMarketDataBridge disposed. Total conversions: {Total}, Average time: {Avg:F2}ms, Total time: {TotalTime}ms, Slow conversions: {Slow}",
            total, avg, totalTime, slow);
    }

    /// <summary>
    /// Async processing pipeline for high-throughput data handling
    /// Phase 2: Eliminates Task.Run overhead with dedicated channel processing
    /// </summary>
    private async Task ProcessChannelDataAsync()
    {
        await foreach (var atasData in _processingChannel.Reader.ReadAllAsync())
        {
            try
            {
                var coreData = ConvertToCore(atasData);

                // Forward to Core signal generator
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var signal = await _signalGenerator.GenerateSignalAsync(coreData);
                        // Signal events will be fired automatically by the signal generator
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing market data in signal generator");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in channel data processing");
            }
        }
    }
}

/// <summary>
/// Order flow data structure for enhanced analysis
/// Phase 2: Optimized for high-frequency order flow processing
/// </summary>
public class OrderFlowData
{
    /// <summary>
    /// Trade price
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// Trade volume
    /// </summary>
    public long Volume { get; set; }

    /// <summary>
    /// Trade direction (Buy/Sell/Unknown)
    /// </summary>
    public SmartVolumeStrategy.Core.Models.TradeDirection Direction { get; set; }

    /// <summary>
    /// Trade timestamp
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Whether this trade was aggressive (market order vs limit order)
    /// </summary>
    public bool IsAggressive { get; set; }
}

/// <summary>
/// Interface for order flow analysis capabilities
/// Phase 2: Enhanced with high-frequency order flow processing
/// </summary>
public interface IOrderFlowAnalyzer
{
    /// <summary>
    /// Processes order flow data for enhanced market analysis
    /// </summary>
    /// <param name="orderFlowData">Order flow data to analyze</param>
    void ProcessOrderFlow(OrderFlowData orderFlowData);
}
