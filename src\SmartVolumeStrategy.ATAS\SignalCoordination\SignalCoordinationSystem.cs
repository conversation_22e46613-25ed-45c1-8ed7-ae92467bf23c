using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Interfaces;
using SmartVolumeStrategy.Core.Models;
using SmartVolumeStrategy.Core.Indicators;
using SmartVolumeStrategy.ATAS.PressureDetection;
using SmartVolumeStrategy.ATAS.PressureDetection.Phase2;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SmartVolumeStrategy.ATAS.SignalCoordination
{
    /// <summary>
    /// Signal Coordination System - Integrates Phase 1 and Phase 2 detectors
    /// Implements unified signal aggregation with weighted confidence scoring
    /// Phase 1: 40% weight, Phase 2: 60% weight, Early warnings: +10-15% boost
    /// CRITICAL: Implements ISignalGenerator for AdaptiveSystemCoordinator integration
    /// ENHANCED: Supports Smart Indicator Selection for adaptive indicator management
    /// </summary>
    public class SignalCoordinationSystem : ISignalGenerator, IDisposable
    {
        private readonly ILogger<SignalCoordinationSystem> _logger;
        private readonly ISignalGenerator _phase1SignalGenerator;
        
        // Phase 2 Detectors
        private readonly IcebergOrderDetector _icebergDetector;
        private readonly SpoofingDetector _spoofingDetector;
        private readonly BlockTradeIdentifier _blockTradeIdentifier;
        private readonly PressureZScoreAnalyzer _pressureZScoreAnalyzer;
        private readonly VolumeClusteringAnalyzer _volumeClusteringAnalyzer;
        private readonly LiquidityFragmentationAnalyzer _liquidityFragmentationAnalyzer;
        
        // Configuration
        private const double Phase1Weight = 0.40; // Phase 1 detectors contribute 40%
        private const double Phase2Weight = 0.60; // Phase 2 detectors contribute 60%
        private const double EarlyWarningBoostMin = 0.10; // 10% minimum boost
        private const double EarlyWarningBoostMax = 0.15; // 15% maximum boost
        private const double ConflictResolutionThreshold = 0.05; // 5% confidence difference for tie-breaking
        private const int MaxProcessingTimeMs = 20; // Maximum total processing time

        // Configurable thresholds
        private double _confidenceThreshold = 0.85; // Default to 85%, but configurable
        private int _indicatorAlignmentThreshold = 3; // Conservative default, will be overridden by user configuration
        
        // Performance tracking
        private readonly object _statsLock = new object();
        private long _totalSignalsProcessed;
        private long _phase1Signals;
        private long _phase2Signals;
        private long _combinedSignals;
        private long _conflictResolutions;
        private long _earlyWarningBoosts;
        private double _averageProcessingTimeMs;
        private readonly List<double> _processingTimes = new List<double>();
        
        // Signal conflict tracking
        private readonly Dictionary<string, int> _detectorReliabilityScores;
        private DateTime _lastSignalTime = DateTime.MinValue;
        
        public SignalCoordinationSystem(
            ILogger<SignalCoordinationSystem> logger,
            ISignalGenerator phase1SignalGenerator,
            IcebergOrderDetector icebergDetector,
            SpoofingDetector spoofingDetector,
            BlockTradeIdentifier blockTradeIdentifier,
            PressureZScoreAnalyzer pressureZScoreAnalyzer,
            VolumeClusteringAnalyzer volumeClusteringAnalyzer,
            LiquidityFragmentationAnalyzer liquidityFragmentationAnalyzer)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _phase1SignalGenerator = phase1SignalGenerator ?? throw new ArgumentNullException(nameof(phase1SignalGenerator));
            _icebergDetector = icebergDetector ?? throw new ArgumentNullException(nameof(icebergDetector));
            _spoofingDetector = spoofingDetector ?? throw new ArgumentNullException(nameof(spoofingDetector));
            _blockTradeIdentifier = blockTradeIdentifier ?? throw new ArgumentNullException(nameof(blockTradeIdentifier));
            _pressureZScoreAnalyzer = pressureZScoreAnalyzer ?? throw new ArgumentNullException(nameof(pressureZScoreAnalyzer));
            _volumeClusteringAnalyzer = volumeClusteringAnalyzer ?? throw new ArgumentNullException(nameof(volumeClusteringAnalyzer));
            _liquidityFragmentationAnalyzer = liquidityFragmentationAnalyzer ?? throw new ArgumentNullException(nameof(liquidityFragmentationAnalyzer));
            
            // Initialize detector reliability scores (higher = more reliable)
            _detectorReliabilityScores = new Dictionary<string, int>
            {
                // Phase 2 detectors (higher priority for institutional activity)
                ["BlockTradeIdentifier"] = 100,
                ["IcebergOrderDetector"] = 95,
                ["LiquidityFragmentationAnalyzer"] = 92, // High reliability for breakout prediction
                ["PressureZScoreAnalyzer"] = 90,
                ["VolumeClusteringAnalyzer"] = 85,
                ["SpoofingDetector"] = 80,
                
                // Phase 1 detectors (base reliability)
                ["VolumeFlowIndex"] = 75,
                ["OrderBookPressureRatio"] = 75,
                ["VWAPDeviationTracker"] = 70,
                ["LargeOrderDetection"] = 70,
                ["BollingerBands"] = 65,
                ["StochasticOscillator"] = 65,
                ["UltraFastRSI"] = 60,
                ["TickBasedMACD"] = 60,
                ["LiquidityGapAnalysis"] = 55
            };
            
            _logger.LogInformation("🎯 Signal Coordination System initialized with Phase 1 ({Phase1Weight:P0}) + Phase 2 ({Phase2Weight:P0}) integration",
                Phase1Weight, Phase2Weight);
        }

        /// <summary>
        /// Sets the confidence threshold for signal validation
        /// </summary>
        public void SetConfidenceThreshold(double threshold)
        {
            _confidenceThreshold = Math.Max(0.0, Math.Min(1.0, threshold));
            _logger.LogInformation("🎯 Signal Coordination confidence threshold set to {Threshold:P1}", _confidenceThreshold);

            // Also forward to Phase 1 signal generator
            _phase1SignalGenerator.SetConfidenceThreshold(threshold);
        }

        /// <summary>
        /// Sets the indicator alignment threshold for signal validation
        /// </summary>
        public void SetIndicatorAlignmentThreshold(int threshold)
        {
            _indicatorAlignmentThreshold = Math.Max(1, threshold);
            _logger.LogInformation("🎯 Signal Coordination indicator alignment threshold set to {Threshold}", _indicatorAlignmentThreshold);
        }

        /// <summary>
        /// CRITICAL FIX: Enables Smart Indicator Selection if supported by Phase 1 generator
        /// </summary>
        public void EnableSmartIndicatorSelection(bool enabled = true)
        {
            try
            {
                // Check if Phase 1 generator supports smart selection
                if (_phase1SignalGenerator is HighProbabilitySignalGenerator hpsg)
                {
                    hpsg.SetSmartSelectionEnabled(enabled);
                    _logger.LogInformation("✅ Smart Indicator Selection {Status} in Phase 1 generator",
                        enabled ? "ENABLED" : "DISABLED");
                }
                else
                {
                    _logger.LogWarning("⚠️ Smart Indicator Selection not supported by current Phase 1 generator: {GeneratorType}",
                        _phase1SignalGenerator?.GetType().Name ?? "Unknown");
                    _logger.LogInformation("💡 To enable: Use HighProbabilitySignalGenerator with ISmartIndicatorSelectionEngine");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error enabling Smart Indicator Selection");
            }
        }
        
        /// <summary>
        /// Generates coordinated signal by combining Phase 1 and Phase 2 detectors
        /// </summary>
        public async Task<CoordinatedSignalResult> GenerateCoordinatedSignalAsync(
            MarketData marketData, 
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _totalSignalsProcessed++;
                
                // Phase 1: Get traditional indicator-based signal
                var phase1Task = _phase1SignalGenerator.GenerateSignalAsync(marketData, cancellationToken);
                
                // Phase 2: Run all enhanced pressure detectors in parallel
                var icebergTask = Task.Run(() => _icebergDetector.DetectIceberg(marketData), cancellationToken);
                var spoofingTask = Task.Run(() => _spoofingDetector.DetectSpoofing(marketData), cancellationToken);
                var blockTradeTask = Task.Run(() => _blockTradeIdentifier.IdentifyBlockTrade(marketData), cancellationToken);
                var zScoreTask = Task.Run(() => _pressureZScoreAnalyzer.AnalyzePressureZScore(marketData), cancellationToken);
                var clusteringTask = Task.Run(() => _volumeClusteringAnalyzer.AnalyzeClustering(marketData), cancellationToken);
                var fragmentationTask = Task.Run(() => _liquidityFragmentationAnalyzer.AnalyzeFragmentation(marketData), cancellationToken);

                // Wait for all detectors to complete
                var phase1Signal = await phase1Task;
                var icebergResult = await icebergTask;
                var spoofingResult = await spoofingTask;
                var blockTradeResult = await blockTradeTask;
                var zScoreResult = await zScoreTask;
                var clusteringResult = await clusteringTask;
                var fragmentationResult = await fragmentationTask;

                var phase2Results = new object[] { icebergResult, spoofingResult, blockTradeResult, zScoreResult, clusteringResult, fragmentationResult };
                
                stopwatch.Stop();
                var processingTimeMs = stopwatch.Elapsed.TotalMilliseconds;
                
                // Performance validation
                if (processingTimeMs > MaxProcessingTimeMs)
                {
                    _logger.LogWarning("⚠️ Signal coordination exceeded {MaxTime}ms: {ActualTime:F2}ms", 
                        MaxProcessingTimeMs, processingTimeMs);
                }
                
                // Create coordinated result
                var coordinatedResult = CoordinateSignals(marketData, phase1Signal, phase2Results, processingTimeMs);
                
                // Update statistics
                UpdateStatistics(coordinatedResult, processingTimeMs);
                
                return coordinatedResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error in signal coordination for {Symbol} at {Timestamp}", 
                    marketData.Symbol, marketData.Timestamp);
                
                return CreateErrorResult(marketData, ex.Message);
            }
        }
        
        /// <summary>
        /// Coordinates signals from Phase 1 and Phase 2 detectors
        /// </summary>
        private CoordinatedSignalResult CoordinateSignals(
            MarketData marketData,
            TradingSignal phase1Signal,
            object[] phase2Results,
            double processingTimeMs)
        {
            var phase2Signals = ExtractPhase2Signals(phase2Results);
            
            // Calculate weighted confidence scores
            var phase1Confidence = phase1Signal.Confidence * Phase1Weight;
            var phase2Confidence = CalculatePhase2Confidence(phase2Signals) * Phase2Weight;

            // CRITICAL FIX: Enhanced diagnostic logging for 0.00% confidence debugging with fallback
            if (phase1Signal.Confidence == 0.0 && phase2Confidence == 0.0)
            {
                _logger.LogWarning("🚨 ZERO CONFIDENCE DETECTED: Phase1={Phase1Conf:F3}, Phase2={Phase2Conf:F3}, Phase2Signals={Phase2Count}",
                    phase1Signal.Confidence, phase2Confidence, phase2Signals.Count);

                if (phase2Signals.Any())
                {
                    foreach (var p2Signal in phase2Signals)
                    {
                        _logger.LogWarning("   📊 Phase2 Signal: {Type} - Direction={Dir}, Confidence={Conf:F3}",
                            p2Signal.DetectorType, p2Signal.Direction, p2Signal.Confidence);
                    }
                }
                else
                {
                    _logger.LogWarning("   ⚠️ No Phase 2 signals generated - attempting fallback mechanism");

                    // CRITICAL ENHANCEMENT: Try fallback signal generation for demo data
                    var fallbackSignal = DemoDataOptimizedDetectors.FallbackMechanisms.CreateFallbackSignal(marketData, _logger);
                    if (fallbackSignal.IsValid)
                    {
                        _logger.LogInformation($"✅ FALLBACK SUCCESS: Generated {fallbackSignal.Direction} signal with {fallbackSignal.Confidence:P2} confidence");

                        // Add fallback signal to Phase 2 results
                        phase2Signals.Add(new Phase2SignalResult
                        {
                            DetectorType = "FallbackMechanism",
                            Direction = fallbackSignal.Direction,
                            Confidence = fallbackSignal.Confidence,
                            IsEarlyWarning = fallbackSignal.HasEarlyWarning,
                            Timestamp = fallbackSignal.Timestamp,
                            RawResult = fallbackSignal
                        });

                        // Recalculate Phase 2 confidence with fallback
                        phase2Confidence = CalculatePhase2Confidence(phase2Signals) * Phase2Weight;
                    }
                    else
                    {
                        _logger.LogWarning("   ❌ FALLBACK FAILED: No signals generated through any mechanism");
                        _logger.LogWarning("   💡 SUGGESTION: Consider using demo-optimized thresholds for Phase 2 detectors");
                    }
                }
            }

            // Detect early warning signals
            var earlyWarningBoost = CalculateEarlyWarningBoost(phase1Signal, phase2Signals);

            // Calculate final confidence
            var baseConfidence = phase1Confidence + phase2Confidence;
            var finalConfidence = Math.Min(baseConfidence + earlyWarningBoost, 1.0);
            
            // Resolve signal conflicts
            var finalDirection = ResolveSignalConflicts(phase1Signal, phase2Signals, finalConfidence);
            
            // Check if signal meets requirements
            var meetsThreshold = finalConfidence >= _confidenceThreshold; // Use configurable threshold
            var hasAlignment = CheckIndicatorAlignment(phase1Signal, phase2Signals);
            
            var result = new CoordinatedSignalResult
            {
                IsValid = meetsThreshold && hasAlignment && finalDirection != SignalDirection.None,
                Direction = finalDirection,
                Confidence = finalConfidence,
                Phase1Confidence = phase1Confidence,
                Phase2Confidence = phase2Confidence,
                EarlyWarningBoost = earlyWarningBoost,
                Phase1Signal = phase1Signal,
                Phase2Signals = phase2Signals,
                ProcessingTimeMs = processingTimeMs,
                Timestamp = marketData.Timestamp,
                ConflictResolved = _conflictResolutions > 0,
                IndicatorAlignment = GetIndicatorAlignmentCount(phase1Signal, phase2Signals),
                Metadata = CreateMetadata(marketData, phase1Signal, phase2Signals, finalConfidence)
            };
            
            LogSignalCoordination(result);
            return result;
        }
        
        /// <summary>
        /// Extracts Phase 2 signals from detector results
        /// </summary>
        private List<Phase2SignalResult> ExtractPhase2Signals(object[] phase2Results)
        {
            var signals = new List<Phase2SignalResult>();
            
            if (phase2Results[0] is IcebergDetectionResult icebergResult && icebergResult.IsValid)
            {
                signals.Add(new Phase2SignalResult
                {
                    DetectorType = "IcebergOrderDetector",
                    Direction = icebergResult.Direction,
                    Confidence = icebergResult.Confidence,
                    IsEarlyWarning = icebergResult.IsEarlyWarning,
                    Timestamp = icebergResult.Timestamp,
                    RawResult = icebergResult
                });
            }
            
            if (phase2Results[1] is SpoofingDetectionResult spoofingResult && spoofingResult.IsValid)
            {
                signals.Add(new Phase2SignalResult
                {
                    DetectorType = "SpoofingDetector",
                    Direction = spoofingResult.Direction,
                    Confidence = spoofingResult.Confidence,
                    IsEarlyWarning = spoofingResult.IsEarlyWarning,
                    Timestamp = spoofingResult.Timestamp,
                    RawResult = spoofingResult
                });
            }
            
            if (phase2Results[2] is BlockTradeResult blockTradeResult && blockTradeResult.IsValid)
            {
                signals.Add(new Phase2SignalResult
                {
                    DetectorType = "BlockTradeIdentifier",
                    Direction = blockTradeResult.Direction,
                    Confidence = blockTradeResult.Confidence,
                    IsEarlyWarning = blockTradeResult.IsEarlyWarning,
                    Timestamp = blockTradeResult.Timestamp,
                    RawResult = blockTradeResult
                });
            }
            
            if (phase2Results[3] is PressureZScoreResult zScoreResult && zScoreResult.IsValid)
            {
                signals.Add(new Phase2SignalResult
                {
                    DetectorType = "PressureZScoreAnalyzer",
                    Direction = zScoreResult.Direction,
                    Confidence = zScoreResult.Confidence,
                    IsEarlyWarning = zScoreResult.IsEarlyWarning,
                    Timestamp = zScoreResult.Timestamp,
                    RawResult = zScoreResult
                });
            }
            
            if (phase2Results[4] is VolumeClusteringResult clusteringResult && clusteringResult.IsValid)
            {
                signals.Add(new Phase2SignalResult
                {
                    DetectorType = "VolumeClusteringAnalyzer",
                    Direction = clusteringResult.Direction,
                    Confidence = clusteringResult.Confidence,
                    IsEarlyWarning = clusteringResult.IsEarlyWarning,
                    Timestamp = clusteringResult.Timestamp,
                    RawResult = clusteringResult
                });
            }

            if (phase2Results[5] is LiquidityFragmentationResult fragmentationResult && fragmentationResult.IsValid)
            {
                signals.Add(new Phase2SignalResult
                {
                    DetectorType = "LiquidityFragmentationAnalyzer",
                    Direction = fragmentationResult.Direction,
                    Confidence = fragmentationResult.Confidence,
                    IsEarlyWarning = fragmentationResult.IsEarlyWarning,
                    Timestamp = fragmentationResult.Timestamp,
                    RawResult = fragmentationResult
                });
            }

            return signals;
        }

        /// <summary>
        /// Calculates Phase 2 confidence using weighted average
        /// </summary>
        private double CalculatePhase2Confidence(List<Phase2SignalResult> phase2Signals)
        {
            if (!phase2Signals.Any()) return 0.0;

            var totalWeight = 0.0;
            var weightedConfidence = 0.0;

            // Phase 2 detector weights (institutional activity gets higher weight)
            var detectorWeights = new Dictionary<string, double>
            {
                ["BlockTradeIdentifier"] = 0.25,              // Reduced from 0.30 - Institutional trades
                ["LiquidityFragmentationAnalyzer"] = 0.20,    // NEW - Breakout prediction and entry timing
                ["IcebergOrderDetector"] = 0.20,              // Reduced from 0.25 - Hidden institutional orders
                ["PressureZScoreAnalyzer"] = 0.15,            // Reduced from 0.20 - Statistical anomaly detection
                ["VolumeClusteringAnalyzer"] = 0.12,          // Reduced from 0.15 - Support/resistance detection
                ["SpoofingDetector"] = 0.08                   // Reduced from 0.10 - Market manipulation detection
            };

            foreach (var signal in phase2Signals)
            {
                if (detectorWeights.TryGetValue(signal.DetectorType, out var weight))
                {
                    weightedConfidence += signal.Confidence * weight;
                    totalWeight += weight;
                }
            }

            return totalWeight > 0 ? weightedConfidence / totalWeight : 0.0;
        }

        /// <summary>
        /// Calculates early warning boost from any detector flagging early warnings
        /// </summary>
        private double CalculateEarlyWarningBoost(TradingSignal phase1Signal, List<Phase2SignalResult> phase2Signals)
        {
            var earlyWarningCount = 0;
            var totalEarlyWarningConfidence = 0.0;

            // Check Phase 2 early warnings
            foreach (var signal in phase2Signals.Where(s => s.IsEarlyWarning))
            {
                earlyWarningCount++;
                totalEarlyWarningConfidence += signal.Confidence;
            }

            // Check Phase 1 early warnings (if metadata indicates early warning)
            if (phase1Signal.Metadata?.ContainsKey("EarlyWarning") == true &&
                (bool)phase1Signal.Metadata["EarlyWarning"])
            {
                earlyWarningCount++;
                totalEarlyWarningConfidence += phase1Signal.Confidence;
            }

            if (earlyWarningCount == 0) return 0.0;

            // Calculate boost based on early warning strength
            var averageEarlyWarningConfidence = totalEarlyWarningConfidence / earlyWarningCount;
            var boostFactor = Math.Min(earlyWarningCount / 3.0, 1.0); // Max boost with 3+ early warnings

            var boost = EarlyWarningBoostMin + (boostFactor * (EarlyWarningBoostMax - EarlyWarningBoostMin));
            boost *= averageEarlyWarningConfidence; // Scale by confidence

            if (boost > 0)
            {
                _earlyWarningBoosts++;
                _logger.LogDebug("🚨 Early warning boost: {Boost:F3} from {Count} signals", boost, earlyWarningCount);
            }

            return boost;
        }

        /// <summary>
        /// Resolves conflicts between Phase 1 and Phase 2 signals
        /// </summary>
        private SignalDirection ResolveSignalConflicts(
            TradingSignal phase1Signal,
            List<Phase2SignalResult> phase2Signals,
            double finalConfidence)
        {
            var phase1Direction = phase1Signal.Direction;
            var phase2Directions = phase2Signals.Select(s => s.Direction).ToList();

            // If no Phase 2 signals, use Phase 1
            if (!phase2Directions.Any()) return phase1Direction;

            // Count directional consensus
            var buyCount = phase2Directions.Count(d => d == SignalDirection.Buy);
            var sellCount = phase2Directions.Count(d => d == SignalDirection.Sell);

            // Determine Phase 2 consensus
            SignalDirection phase2Consensus;
            if (buyCount > sellCount) phase2Consensus = SignalDirection.Buy;
            else if (sellCount > buyCount) phase2Consensus = SignalDirection.Sell;
            else phase2Consensus = SignalDirection.None;

            // If Phase 1 and Phase 2 agree, no conflict
            if (phase1Direction == phase2Consensus) return phase1Direction;

            // Handle conflicts - prioritize institutional activity (Phase 2)
            if (phase2Consensus != SignalDirection.None)
            {
                // Check if Phase 2 has institutional signals (higher priority)
                var institutionalSignals = phase2Signals.Where(s =>
                    s.DetectorType == "BlockTradeIdentifier" ||
                    s.DetectorType == "IcebergOrderDetector" ||
                    s.DetectorType == "LiquidityFragmentationAnalyzer").ToList();

                if (institutionalSignals.Any())
                {
                    _conflictResolutions++;
                    _logger.LogDebug("🔄 Conflict resolved: Phase 2 institutional activity overrides Phase 1");
                    return phase2Consensus;
                }

                // Use confidence-based tie-breaking
                var phase1Confidence = phase1Signal.Confidence * Phase1Weight;
                var phase2Confidence = CalculatePhase2Confidence(phase2Signals) * Phase2Weight;

                if (Math.Abs(phase2Confidence - phase1Confidence) > ConflictResolutionThreshold)
                {
                    _conflictResolutions++;
                    var winner = phase2Confidence > phase1Confidence ? "Phase 2" : "Phase 1";
                    var direction = phase2Confidence > phase1Confidence ? phase2Consensus : phase1Direction;
                    _logger.LogDebug("🔄 Conflict resolved: {Winner} wins by confidence margin", winner);
                    return direction;
                }

                // Use recency as final tie-breaker (more recent signals preferred)
                var mostRecentPhase2 = phase2Signals.OrderByDescending(s => s.Timestamp).FirstOrDefault();
                if (mostRecentPhase2 != null &&
                    (DateTime.UtcNow - mostRecentPhase2.Timestamp).TotalSeconds <
                    (DateTime.UtcNow - _lastSignalTime).TotalSeconds)
                {
                    _conflictResolutions++;
                    _logger.LogDebug("🔄 Conflict resolved: Phase 2 wins by recency");
                    return phase2Consensus;
                }
            }

            // Default to Phase 1 if no clear resolution
            return phase1Direction;
        }

        /// <summary>
        /// Checks if indicator alignment requirements are met
        /// </summary>
        private bool CheckIndicatorAlignment(TradingSignal phase1Signal, List<Phase2SignalResult> phase2Signals)
        {
            // Get Phase 1 alignment count from metadata
            var phase1Alignment = 0;
            if (phase1Signal.Metadata?.ContainsKey("IndicatorsAligned") == true)
            {
                phase1Alignment = (int)phase1Signal.Metadata["IndicatorsAligned"];
            }

            // Count Phase 2 aligned signals
            var phase2Alignment = phase2Signals.Count;

            // Total alignment (use configurable requirement)
            var totalAlignment = phase1Alignment + phase2Alignment;
            return totalAlignment >= _indicatorAlignmentThreshold;
        }

        /// <summary>
        /// Gets total indicator alignment count
        /// </summary>
        private int GetIndicatorAlignmentCount(TradingSignal phase1Signal, List<Phase2SignalResult> phase2Signals)
        {
            var phase1Count = 0;
            if (phase1Signal.Metadata?.ContainsKey("IndicatorsAligned") == true)
            {
                phase1Count = (int)phase1Signal.Metadata["IndicatorsAligned"];
            }

            return phase1Count + phase2Signals.Count;
        }

        /// <summary>
        /// Creates metadata for coordinated signal result
        /// </summary>
        private Dictionary<string, object> CreateMetadata(
            MarketData marketData,
            TradingSignal phase1Signal,
            List<Phase2SignalResult> phase2Signals,
            double finalConfidence)
        {
            return new Dictionary<string, object>
            {
                ["Symbol"] = marketData.Symbol,
                ["Timestamp"] = marketData.Timestamp,
                ["Phase1Indicators"] = phase1Signal.Metadata?.GetValueOrDefault("IndicatorsAligned", 0) ?? 0,
                ["Phase2Detectors"] = phase2Signals.Count,
                ["TotalAlignment"] = GetIndicatorAlignmentCount(phase1Signal, phase2Signals),
                ["Phase1Weight"] = Phase1Weight,
                ["Phase2Weight"] = Phase2Weight,
                ["EarlyWarnings"] = phase2Signals.Count(s => s.IsEarlyWarning),
                ["ConflictsResolved"] = _conflictResolutions,
                ["ProcessingTimeMs"] = _averageProcessingTimeMs,
                ["CoordinationMethod"] = "SignalCoordinationSystem"
            };
        }

        /// <summary>
        /// Updates performance statistics
        /// </summary>
        private void UpdateStatistics(CoordinatedSignalResult result, double processingTimeMs)
        {
            lock (_statsLock)
            {
                _processingTimes.Add(processingTimeMs);
                if (_processingTimes.Count > 100) // Keep last 100 measurements
                {
                    _processingTimes.RemoveAt(0);
                }

                _averageProcessingTimeMs = _processingTimes.Average();

                if (result.Phase1Signal.Direction != SignalDirection.None) _phase1Signals++;
                if (result.Phase2Signals.Any()) _phase2Signals++;
                if (result.IsValid) _combinedSignals++;
            }
        }

        /// <summary>
        /// Logs signal coordination details
        /// </summary>
        private void LogSignalCoordination(CoordinatedSignalResult result)
        {
            _logger.LogDebug("🎯 Signal Coordination: Valid={IsValid}, Direction={Direction}, " +
                           "Confidence={Confidence:F3} (P1:{Phase1Confidence:F3} + P2:{Phase2Confidence:F3} + EW:{EarlyWarningBoost:F3}), " +
                           "Alignment={Alignment}, Time={ProcessingTimeMs:F2}ms",
                result.IsValid, result.Direction, result.Confidence,
                result.Phase1Confidence, result.Phase2Confidence, result.EarlyWarningBoost,
                result.IndicatorAlignment, result.ProcessingTimeMs);
        }

        /// <summary>
        /// Creates error result for exception handling
        /// </summary>
        private CoordinatedSignalResult CreateErrorResult(MarketData marketData, string errorMessage)
        {
            return new CoordinatedSignalResult
            {
                IsValid = false,
                Direction = SignalDirection.None,
                Confidence = 0.0,
                Phase1Confidence = 0.0,
                Phase2Confidence = 0.0,
                EarlyWarningBoost = 0.0,
                Phase1Signal = new TradingSignal { Symbol = marketData.Symbol, Direction = SignalDirection.None },
                Phase2Signals = new List<Phase2SignalResult>(),
                ProcessingTimeMs = 0.0,
                Timestamp = marketData.Timestamp,
                ConflictResolved = false,
                IndicatorAlignment = 0,
                Metadata = new Dictionary<string, object> { ["Error"] = errorMessage }
            };
        }

        /// <summary>
        /// Gets performance statistics
        /// </summary>
        public SignalCoordinationStats GetStatistics()
        {
            lock (_statsLock)
            {
                return new SignalCoordinationStats
                {
                    TotalSignalsProcessed = _totalSignalsProcessed,
                    Phase1Signals = _phase1Signals,
                    Phase2Signals = _phase2Signals,
                    CombinedSignals = _combinedSignals,
                    ConflictResolutions = _conflictResolutions,
                    EarlyWarningBoosts = _earlyWarningBoosts,
                    AverageProcessingTimeMs = _averageProcessingTimeMs,
                    SuccessRate = _totalSignalsProcessed > 0 ? (double)_combinedSignals / _totalSignalsProcessed : 0.0
                };
            }
        }

        /// <summary>
        /// CRITICAL DEBUG: Gets Phase 2 detector optimization recommendations for demo data
        /// </summary>
        public Dictionary<string, object> GetPhase2OptimizationRecommendations()
        {
            _logger.LogInformation("🔧 PHASE 2 OPTIMIZATION ANALYSIS:");

            // Get current detector performance
            var detectorPerformance = AnalyzeDetectorPerformance();

            // Get demo-optimized configuration recommendations
            var demoRecommendations = Phase2DetectorFactory.ConfigurationRecommendations.GetDemoOptimizationRecommendations();

            // Log recommendations
            Phase2DetectorFactory.ConfigurationRecommendations.LogRecommendations(_logger);

            // Combine performance analysis with recommendations
            var optimizationReport = new Dictionary<string, object>
            {
                ["DetectorPerformance"] = detectorPerformance,
                ["DemoOptimizedThresholds"] = demoRecommendations,
                ["RecommendedActions"] = GeneratePhase2ActionRecommendations(detectorPerformance),
                ["FallbackMechanismStatus"] = "Enabled for demo data compatibility",
                ["AdaptiveThresholdStatus"] = "Available for real-time optimization"
            };

            return optimizationReport;
        }

        /// <summary>
        /// Analyzes current Phase 2 detector performance
        /// </summary>
        private Dictionary<string, object> AnalyzeDetectorPerformance()
        {
            var performance = new Dictionary<string, object>();

            // Calculate success rates for each detector type
            var detectorTypes = new[] { "IcebergOrderDetector", "SpoofingDetector", "BlockTradeIdentifier",
                                      "PressureZScoreAnalyzer", "VolumeClusteringAnalyzer", "LiquidityFragmentationAnalyzer" };

            foreach (var detectorType in detectorTypes)
            {
                var successRate = CalculateDetectorSuccessRate(detectorType);
                var status = successRate switch
                {
                    < 0.05 => "🔴 CRITICAL - Very low success rate, needs optimization",
                    < 0.15 => "🟡 LOW - Below target, consider threshold adjustment",
                    > 0.35 => "🟠 HIGH - Above target, may be too sensitive",
                    _ => "🟢 OPTIMAL - Success rate in target range"
                };

                performance[detectorType] = new { SuccessRate = successRate, Status = status };
            }

            return performance;
        }

        /// <summary>
        /// Calculates success rate for a specific detector type
        /// </summary>
        private double CalculateDetectorSuccessRate(string detectorType)
        {
            // This would be implemented with actual tracking data
            // For now, return a placeholder based on detector reliability scores
            if (_detectorReliabilityScores.TryGetValue(detectorType, out var reliability))
            {
                // Convert reliability score to estimated success rate for demo data
                return Math.Max(0.0, (reliability - 50) / 500.0); // Scale 50-100 to 0.0-0.1
            }
            return 0.05; // Default low success rate for demo data
        }

        /// <summary>
        /// Generates specific action recommendations for Phase 2 optimization
        /// </summary>
        private List<string> GeneratePhase2ActionRecommendations(Dictionary<string, object> performance)
        {
            var recommendations = new List<string>();

            recommendations.Add("1. Apply demo-optimized thresholds (80-95% reduction from production values)");
            recommendations.Add("2. Enable adaptive threshold management for real-time optimization");
            recommendations.Add("3. Use fallback mechanisms when all Phase 2 detectors fail");
            recommendations.Add("4. Monitor detector success rates and adjust thresholds accordingly");
            recommendations.Add("5. Consider reducing minimum sample sizes for statistical analysis");
            recommendations.Add("6. Implement volume-based scaling for demo data characteristics");

            // Add specific recommendations based on performance
            foreach (var kvp in performance)
            {
                var detectorName = kvp.Key;
                if (kvp.Value is object perfObj)
                {
                    var perfType = perfObj.GetType();
                    var successRateProp = perfType.GetProperty("SuccessRate");
                    if (successRateProp != null && successRateProp.GetValue(perfObj) is double successRate && successRate < 0.10)
                    {
                        recommendations.Add($"7. URGENT: {detectorName} has very low success rate - apply immediate threshold reduction");
                    }
                }
            }

            return recommendations;
        }

        #region ISignalGenerator Implementation

        /// <summary>
        /// ISignalGenerator implementation - bridges to coordinated signal generation
        /// CRITICAL: Enables AdaptiveSystemCoordinator integration with LiquidityFragmentationAnalyzer
        /// </summary>
        public async Task<TradingSignal> GenerateSignalAsync(MarketData marketData, CancellationToken cancellationToken = default)
        {
            try
            {
                // Generate coordinated signal using full Phase 1 + Phase 2 integration
                var coordinatedResult = await GenerateCoordinatedSignalAsync(marketData, cancellationToken);

                // Convert to TradingSignal format for ISignalGenerator interface
                var signal = new TradingSignal
                {
                    Symbol = coordinatedResult.Phase1Signal.Symbol,
                    Direction = coordinatedResult.Direction,
                    Confidence = coordinatedResult.Confidence,
                    EntryPrice = coordinatedResult.Phase1Signal.EntryPrice,
                    Timestamp = coordinatedResult.Timestamp,
                    SourceData = marketData,
                    Metadata = new Dictionary<string, object>(coordinatedResult.Metadata)
                    {
                        ["CoordinatedSignal"] = true,
                        ["Phase1Confidence"] = coordinatedResult.Phase1Confidence,
                        ["Phase2Confidence"] = coordinatedResult.Phase2Confidence,
                        ["EarlyWarningBoost"] = coordinatedResult.EarlyWarningBoost,
                        ["IndicatorAlignment"] = coordinatedResult.IndicatorAlignment,
                        ["ProcessingTimeMs"] = coordinatedResult.ProcessingTimeMs,
                        ["LiquidityFragmentationIncluded"] = coordinatedResult.Phase2Signals.Any(s => s.DetectorType == "LiquidityFragmentationAnalyzer")
                    }
                };

                // Trigger high-confidence signal event if applicable
                if (signal.Confidence >= _confidenceThreshold && signal.Direction != SignalDirection.None)
                {
                    HighConfidenceSignalGenerated?.Invoke(this, signal);
                }

                return signal;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ISignalGenerator.GenerateSignalAsync");

                // Return safe fallback signal
                return new TradingSignal
                {
                    Symbol = marketData.Symbol,
                    Direction = SignalDirection.None,
                    Confidence = 0.0,
                    EntryPrice = marketData.Price,
                    Timestamp = marketData.Timestamp,
                    SourceData = marketData,
                    Metadata = new Dictionary<string, object>
                    {
                        ["Error"] = ex.Message,
                        ["CoordinatedSignal"] = false
                    }
                };
            }
        }

        /// <summary>
        /// Confidence threshold property for ISignalGenerator interface
        /// </summary>
        public double ConfidenceThreshold => _confidenceThreshold;

        /// <summary>
        /// Generates multiple signals for batch processing
        /// </summary>
        public async Task<IEnumerable<TradingSignal>> GenerateSignalBatchAsync(
            IEnumerable<MarketData> marketDataBatch,
            CancellationToken cancellationToken = default)
        {
            var signals = new List<TradingSignal>();

            foreach (var marketData in marketDataBatch)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var signal = await GenerateSignalAsync(marketData, cancellationToken);
                signals.Add(signal);
            }

            return signals;
        }

        /// <summary>
        /// Updates signal generator with new market data for real-time processing
        /// </summary>
        public void UpdateMarketData(MarketData marketData)
        {
            // Forward to Phase 1 signal generator for real-time updates
            _phase1SignalGenerator.UpdateMarketData(marketData);
        }

        /// <summary>
        /// Gets signal generation statistics compatible with ISignalGenerator interface
        /// </summary>
        SignalGenerationStats ISignalGenerator.GetStatistics()
        {
            var coordinationStats = GetStatistics();
            return new SignalGenerationStats
            {
                TotalSignalsGenerated = (int)coordinationStats.TotalSignalsProcessed,
                HighConfidenceSignals = (int)coordinationStats.CombinedSignals,
                AverageConfidence = 0.85, // Default coordination confidence
                AverageProcessingTimeMs = coordinationStats.AverageProcessingTimeMs,
                LastSignalTime = DateTime.UtcNow,
                UpTime = TimeSpan.FromMinutes(1) // Placeholder
            };
        }

        /// <summary>
        /// Event fired when a high-confidence signal is generated
        /// </summary>
        public event EventHandler<TradingSignal>? HighConfidenceSignalGenerated;

        /// <summary>
        /// Event fired when signal generation performance metrics are updated
        /// </summary>
#pragma warning disable CS0067 // Event is never used - required by ISignalGenerator interface
        public event EventHandler<SignalGenerationStats>? StatisticsUpdated;
#pragma warning restore CS0067

        #endregion

        public void Dispose()
        {
            var stats = GetStatistics();
            _logger.LogInformation("📊 Signal Coordination System Statistics:");
            _logger.LogInformation("   📊 Total Signals Processed: {TotalSignalsProcessed:N0}", stats.TotalSignalsProcessed);
            _logger.LogInformation("   📊 Phase 1 Signals: {Phase1Signals:N0}", stats.Phase1Signals);
            _logger.LogInformation("   📊 Phase 2 Signals: {Phase2Signals:N0}", stats.Phase2Signals);
            _logger.LogInformation("   📊 Combined Valid Signals: {CombinedSignals:N0}", stats.CombinedSignals);
            _logger.LogInformation("   📊 Conflict Resolutions: {ConflictResolutions:N0}", stats.ConflictResolutions);
            _logger.LogInformation("   📊 Early Warning Boosts: {EarlyWarningBoosts:N0}", stats.EarlyWarningBoosts);
            _logger.LogInformation("   📊 Average Processing Time: {AverageProcessingTimeMs:F2}ms", stats.AverageProcessingTimeMs);
            _logger.LogInformation("   📊 Success Rate: {SuccessRate:P2}", stats.SuccessRate);
        }
    }
}
