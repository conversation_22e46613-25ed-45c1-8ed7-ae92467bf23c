using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Models;
using SmartVolumeStrategy.Core.Infrastructure.MemoryManagement;

namespace SmartVolumeStrategy.Core.Adaptive;

/// <summary>
/// Manages dynamic parameter adjustments based on market regime and performance
/// </summary>
public class AdaptiveParameterManager : IDisposable
{
    private readonly ILogger<AdaptiveParameterManager> _logger;
    private readonly CircularBuffer<ParameterAdjustment> _adjustmentHistory;
    private readonly Dictionary<MarketRegime, RegimeParameterSet> _regimeParameters;
    private readonly AdaptiveSettings _settings;
    
    private bool _disposed;
    private DateTime _lastAdaptation;
    private MarketRegime _currentRegime;
    private readonly object _lock = new();

    // User configuration base values (will be set during initialization)
    private double _userBaseConfidenceThreshold = 0.85;
    private int _userBaseIndicatorAlignment = 3;

    public AdaptiveParameterManager(ILogger<AdaptiveParameterManager> logger, AdaptiveSettings settings)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        
        _adjustmentHistory = new CircularBuffer<ParameterAdjustment>(1000);
        _regimeParameters = InitializeRegimeParameters();
        _lastAdaptation = DateTime.UtcNow;
        _currentRegime = MarketRegime.Unknown;
        
        _logger.LogInformation("AdaptiveParameterManager initialized with {RegimeCount} regime configurations", 
            _regimeParameters.Count);
    }

    /// <summary>
    /// Current adaptive settings
    /// </summary>
    public AdaptiveSettings Settings => _settings;

    /// <summary>
    /// Time of last parameter adaptation
    /// </summary>
    public DateTime LastAdaptation => _lastAdaptation;

    /// <summary>
    /// Number of adaptations performed
    /// </summary>
    public int AdaptationCount => _adjustmentHistory.Count;

    /// <summary>
    /// Recent adjustment history
    /// </summary>
    public IEnumerable<ParameterAdjustment> RecentAdjustments =>
        _adjustmentHistory.ToArray().TakeLast(50);

    /// <summary>
    /// Sets the user's base configuration values for adaptive adjustments
    /// </summary>
    public void SetUserBaseConfiguration(double confidenceThreshold, int indicatorAlignment)
    {
        lock (_lock)
        {
            _userBaseConfidenceThreshold = confidenceThreshold;
            _userBaseIndicatorAlignment = indicatorAlignment;

            _logger.LogInformation("✅ ADAPTIVE SYSTEM: User base configuration updated");
            _logger.LogInformation("   📊 Base Confidence Threshold: {ConfidenceThreshold:P0}", confidenceThreshold);
            _logger.LogInformation("   🎯 Base Indicator Alignment: {IndicatorAlignment} indicators", indicatorAlignment);
        }
    }

    /// <summary>
    /// Adapts parameters based on market regime change
    /// </summary>
    public Task<IEnumerable<ParameterAdjustment>> AdaptToRegimeAsync(
        MarketRegime newRegime,
        AdaptivePerformanceMetrics? performanceMetrics = null)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(AdaptiveParameterManager));

        if (!_settings.EnableAutoAdaptation)
        {
            _logger.LogDebug("Auto-adaptation disabled, skipping regime adaptation");
            return Task.FromResult(Enumerable.Empty<ParameterAdjustment>());
        }

        lock (_lock)
        {
            // Check minimum adaptation interval
            var timeSinceLastAdaptation = DateTime.UtcNow - _lastAdaptation;
            if (timeSinceLastAdaptation.TotalMinutes < _settings.MinAdaptationIntervalMinutes)
            {
                _logger.LogDebug("Adaptation interval not met, skipping adaptation");
                return Task.FromResult(Enumerable.Empty<ParameterAdjustment>());
            }

            var adjustments = new List<ParameterAdjustment>();

            try
            {
                // Get regime-specific parameters
                if (_regimeParameters.TryGetValue(newRegime, out var regimeParams))
                {
                    adjustments.AddRange(ApplyRegimeParameters(newRegime, regimeParams, performanceMetrics));
                }

                // Apply performance-based adjustments if metrics provided
                if (performanceMetrics != null)
                {
                    adjustments.AddRange(ApplyPerformanceAdjustments(performanceMetrics));
                }

                // Record adjustments
                foreach (var adjustment in adjustments)
                {
                    _adjustmentHistory.Add(adjustment);
                }

                _currentRegime = newRegime;
                _lastAdaptation = DateTime.UtcNow;

                _logger.LogInformation("Applied {AdjustmentCount} parameter adjustments for regime {Regime}",
                    adjustments.Count, newRegime);

                return Task.FromResult<IEnumerable<ParameterAdjustment>>(adjustments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during regime adaptation to {Regime}", newRegime);
                return Task.FromResult(Enumerable.Empty<ParameterAdjustment>());
            }
        }
    }

    /// <summary>
    /// Adapts parameters based on performance metrics
    /// </summary>
    public Task<IEnumerable<ParameterAdjustment>> AdaptToPerformanceAsync(
        AdaptivePerformanceMetrics performanceMetrics,
        AdaptationTrigger trigger)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(AdaptiveParameterManager));

        if (!_settings.EnableAutoAdaptation)
            return Task.FromResult(Enumerable.Empty<ParameterAdjustment>());

        lock (_lock)
        {
            var adjustments = new List<ParameterAdjustment>();

            try
            {
                switch (trigger.Type)
                {
                    case AdaptationTriggerType.LowWinRate:
                        adjustments.AddRange(HandleLowWinRate(performanceMetrics, trigger));
                        break;
                        
                    case AdaptationTriggerType.LowSignalFrequency:
                        adjustments.AddRange(HandleLowSignalFrequency(performanceMetrics, trigger));
                        break;
                        
                    case AdaptationTriggerType.PerformanceDegradation:
                        adjustments.AddRange(HandlePerformanceDegradation(performanceMetrics, trigger));
                        break;
                        
                    case AdaptationTriggerType.VolatilitySpike:
                        adjustments.AddRange(HandleVolatilitySpike(performanceMetrics, trigger));
                        break;
                }

                // Record adjustments
                foreach (var adjustment in adjustments)
                {
                    _adjustmentHistory.Add(adjustment);
                }

                if (adjustments.Any())
                {
                    _lastAdaptation = DateTime.UtcNow;
                    _logger.LogInformation("Applied {AdjustmentCount} performance-based adjustments for trigger {TriggerType}", 
                        adjustments.Count, trigger.Type);
                }

                return Task.FromResult<IEnumerable<ParameterAdjustment>>(adjustments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during performance adaptation for trigger {TriggerType}", trigger.Type);
                return Task.FromResult(Enumerable.Empty<ParameterAdjustment>());
            }
        }
    }

    /// <summary>
    /// Gets current parameter values for a specific component
    /// </summary>
    public Dictionary<string, object> GetCurrentParameters(string component)
    {
        // Return user base parameters adjusted for current regime
        var parameters = new Dictionary<string, object>();

        if (_regimeParameters.TryGetValue(_currentRegime, out var regimeParams))
        {
            // Apply regime-specific adjustments to USER BASE parameters (not hardcoded values)
            parameters["ConfidenceThreshold"] = _userBaseConfidenceThreshold + regimeParams.ConfidenceThresholdAdjustment;
            parameters["IndicatorAlignment"] = _userBaseIndicatorAlignment + regimeParams.IndicatorAlignmentAdjustment;
            parameters["VolumeThresholdMultiplier"] = regimeParams.VolumeThresholdMultiplier;
        }
        else
        {
            // No regime adjustments - return user base values
            parameters["ConfidenceThreshold"] = _userBaseConfidenceThreshold;
            parameters["IndicatorAlignment"] = _userBaseIndicatorAlignment;
            parameters["VolumeThresholdMultiplier"] = 1.0;
        }

        return parameters;
    }

    /// <summary>
    /// Applies regime-specific parameter adjustments
    /// </summary>
    private IEnumerable<ParameterAdjustment> ApplyRegimeParameters(
        MarketRegime regime, 
        RegimeParameterSet regimeParams,
        AdaptivePerformanceMetrics? performanceMetrics)
    {
        var adjustments = new List<ParameterAdjustment>();
        var aggressivenessMultiplier = GetAggressivenessMultiplier();

        // Confidence threshold adjustment
        if (Math.Abs(regimeParams.ConfidenceThresholdAdjustment) > 0.001)
        {
            var adjustment = regimeParams.ConfidenceThresholdAdjustment * aggressivenessMultiplier;
            adjustments.Add(new ParameterAdjustment
            {
                Component = "SignalGenerator",
                ParameterName = "ConfidenceThreshold",
                OldValue = _userBaseConfidenceThreshold,
                NewValue = _userBaseConfidenceThreshold + adjustment,
                Reason = $"Regime adaptation to {regime}",
                TriggeringRegime = regime,
                PerformanceMetrics = performanceMetrics?.ToDictionary() ?? new()
            });
        }

        // Indicator alignment adjustment
        if (regimeParams.IndicatorAlignmentAdjustment != 0)
        {
            var adjustment = (int)(regimeParams.IndicatorAlignmentAdjustment * aggressivenessMultiplier);
            adjustments.Add(new ParameterAdjustment
            {
                Component = "SignalGenerator",
                ParameterName = "MinIndicatorAlignment",
                OldValue = _userBaseIndicatorAlignment,
                NewValue = _userBaseIndicatorAlignment + adjustment,
                Reason = $"Regime adaptation to {regime}",
                TriggeringRegime = regime,
                PerformanceMetrics = performanceMetrics?.ToDictionary() ?? new()
            });
        }

        // Volume threshold multiplier adjustment
        if (Math.Abs(regimeParams.VolumeThresholdMultiplier - 1.0) > 0.01)
        {
            adjustments.Add(new ParameterAdjustment
            {
                Component = "VolumeIndicators",
                ParameterName = "VolumeThresholdMultiplier",
                OldValue = 1.0,
                NewValue = regimeParams.VolumeThresholdMultiplier * aggressivenessMultiplier,
                Reason = $"Regime adaptation to {regime} - volume sensitivity",
                TriggeringRegime = regime,
                PerformanceMetrics = performanceMetrics?.ToDictionary() ?? new()
            });
        }

        // VWAP deviation adjustment
        if (Math.Abs(regimeParams.VWAPDeviationAdjustment) > 0.001)
        {
            var adjustment = regimeParams.VWAPDeviationAdjustment * aggressivenessMultiplier;
            adjustments.Add(new ParameterAdjustment
            {
                Component = "VWAPIndicator",
                ParameterName = "DeviationThreshold",
                OldValue = 0.001, // Default VWAP deviation threshold (0.1%)
                NewValue = 0.001 + adjustment,
                Reason = $"Regime adaptation to {regime} - VWAP sensitivity",
                TriggeringRegime = regime,
                PerformanceMetrics = performanceMetrics?.ToDictionary() ?? new()
            });
        }

        return adjustments;
    }

    /// <summary>
    /// Applies performance-based parameter adjustments
    /// </summary>
    private IEnumerable<ParameterAdjustment> ApplyPerformanceAdjustments(AdaptivePerformanceMetrics performanceMetrics)
    {
        var adjustments = new List<ParameterAdjustment>();
        var sensitivity = _settings.LearningSensitivity;

        // CRITICAL FIX: Improved win rate adjustment logic to prevent over-restriction
        if (performanceMetrics.WinRate < _settings.MinWinRateThreshold)
        {
            // CRITICAL FIX: Special handling for single-position scenarios
            if (performanceMetrics.TotalSignals <= 1)
            {
                // Don't adjust based on single trade - insufficient data
                _logger.LogWarning("Skipping confidence adjustment: Insufficient trade data (TotalSignals: {TotalSignals})",
                    performanceMetrics.TotalSignals);
                return adjustments;
            }

            // CRITICAL FIX: Limit maximum confidence threshold to prevent system lockup
            var maxAllowedThreshold = 0.90; // Never exceed 90% confidence
            var currentThreshold = _userBaseConfidenceThreshold;

            if (currentThreshold >= maxAllowedThreshold)
            {
                _logger.LogWarning("Confidence threshold already at maximum ({MaxThreshold:P1}), skipping adjustment",
                    maxAllowedThreshold);
                return adjustments;
            }

            // CRITICAL FIX: Reduced adjustment magnitude to prevent over-restriction
            var adjustment = Math.Min(_settings.MaxParameterAdjustment * sensitivity * 0.5, // 50% reduction
                                    maxAllowedThreshold - currentThreshold); // Don't exceed max

            var newThreshold = currentThreshold + adjustment;

            _logger.LogInformation("ADAPTIVE ADJUSTMENT: WinRate {WinRate:P1} < {MinThreshold:P1}, " +
                                 "adjusting confidence {OldThreshold:P1} -> {NewThreshold:P1} (limited adjustment)",
                performanceMetrics.WinRate, _settings.MinWinRateThreshold, currentThreshold, newThreshold);

            adjustments.Add(new ParameterAdjustment
            {
                Component = "SignalGenerator",
                ParameterName = "ConfidenceThreshold",
                OldValue = currentThreshold,
                NewValue = newThreshold,
                Reason = $"Low win rate: {performanceMetrics.WinRate:P1} (limited adjustment to prevent over-restriction)",
                PerformanceMetrics = performanceMetrics.ToDictionary()
            });
        }

        return adjustments;
    }

    /// <summary>
    /// Handles low win rate adaptation
    /// </summary>
    private IEnumerable<ParameterAdjustment> HandleLowWinRate(
        AdaptivePerformanceMetrics performanceMetrics, 
        AdaptationTrigger trigger)
    {
        var adjustments = new List<ParameterAdjustment>();
        var sensitivity = _settings.LearningSensitivity;

        // CRITICAL FIX: Enhanced selectivity adjustment with safety limits
        var maxAllowedThreshold = 0.90; // Never exceed 90% confidence
        var currentThreshold = (double)trigger.Context.GetValueOrDefault("CurrentConfidenceThreshold", _userBaseConfidenceThreshold);

        if (currentThreshold >= maxAllowedThreshold)
        {
            _logger.LogWarning("Confidence threshold already at maximum ({MaxThreshold:P1}), skipping low win rate adjustment",
                maxAllowedThreshold);
            return adjustments;
        }

        // CRITICAL FIX: Reduced adjustment magnitude and safety cap
        var confidenceAdjustment = Math.Min(_settings.MaxParameterAdjustment * sensitivity * 0.5, // 50% reduction
                                          maxAllowedThreshold - currentThreshold); // Don't exceed max

        var newThreshold = currentThreshold + confidenceAdjustment;

        _logger.LogInformation("LOW WIN RATE TRIGGER: Adjusting confidence {OldThreshold:P1} -> {NewThreshold:P1} " +
                             "(WinRate: {WinRate:P1}, limited adjustment)",
            currentThreshold, newThreshold, performanceMetrics.WinRate);

        adjustments.Add(new ParameterAdjustment
        {
            Component = "SignalGenerator",
            ParameterName = "ConfidenceThreshold",
            OldValue = currentThreshold,
            NewValue = newThreshold,
            Reason = $"Low win rate trigger: {performanceMetrics.WinRate:P1} < {trigger.ThresholdValue:P1} (limited adjustment)",
            PerformanceMetrics = performanceMetrics.ToDictionary()
        });

        return adjustments;
    }

    /// <summary>
    /// Handles low signal frequency adaptation
    /// </summary>
    private IEnumerable<ParameterAdjustment> HandleLowSignalFrequency(
        AdaptivePerformanceMetrics performanceMetrics, 
        AdaptationTrigger trigger)
    {
        var adjustments = new List<ParameterAdjustment>();
        var sensitivity = _settings.LearningSensitivity;

        // Decrease selectivity by lowering confidence threshold
        var confidenceAdjustment = -_settings.MaxParameterAdjustment * sensitivity;
        var currentThreshold = (double)trigger.Context.GetValueOrDefault("CurrentConfidenceThreshold", _userBaseConfidenceThreshold);
        adjustments.Add(new ParameterAdjustment
        {
            Component = "SignalGenerator",
            ParameterName = "ConfidenceThreshold",
            OldValue = currentThreshold,
            NewValue = currentThreshold + confidenceAdjustment,
            Reason = $"Low signal frequency: {performanceMetrics.SignalFrequency:F1} signals/hour",
            PerformanceMetrics = performanceMetrics.ToDictionary()
        });

        return adjustments;
    }

    /// <summary>
    /// Handles performance degradation adaptation
    /// </summary>
    private IEnumerable<ParameterAdjustment> HandlePerformanceDegradation(
        AdaptivePerformanceMetrics performanceMetrics, 
        AdaptationTrigger trigger)
    {
        // Similar to low win rate but more conservative
        return HandleLowWinRate(performanceMetrics, trigger)
            .Select(adj => adj with { 
                NewValue = (double)adj.NewValue! * 0.5, // More conservative adjustment
                Reason = $"Performance degradation detected: {adj.Reason}"
            });
    }

    /// <summary>
    /// Handles volatility spike adaptation
    /// </summary>
    private IEnumerable<ParameterAdjustment> HandleVolatilitySpike(
        AdaptivePerformanceMetrics performanceMetrics, 
        AdaptationTrigger trigger)
    {
        var adjustments = new List<ParameterAdjustment>();

        // Increase all thresholds during high volatility
        var volatilityMultiplier = Math.Min(trigger.CurrentValue / trigger.ThresholdValue, 2.0);
        var adjustment = _settings.MaxParameterAdjustment * volatilityMultiplier * _settings.LearningSensitivity;

        adjustments.Add(new ParameterAdjustment
        {
            Component = "SignalGenerator",
            ParameterName = "ConfidenceThreshold",
            OldValue = _userBaseConfidenceThreshold,
            NewValue = _userBaseConfidenceThreshold + adjustment,
            Reason = $"Volatility spike: {trigger.CurrentValue:F2} > {trigger.ThresholdValue:F2}",
            PerformanceMetrics = performanceMetrics.ToDictionary()
        });

        return adjustments;
    }

    /// <summary>
    /// Gets aggressiveness multiplier based on user settings
    /// </summary>
    private double GetAggressivenessMultiplier()
    {
        return _settings.AggressivenessLevel switch
        {
            1 => 0.5,  // Conservative
            2 => 0.75,
            3 => 1.0,  // Balanced
            4 => 1.25,
            5 => 1.5,  // Aggressive
            _ => 1.0
        };
    }

    /// <summary>
    /// Initializes regime-specific parameter sets
    /// </summary>
    private Dictionary<MarketRegime, RegimeParameterSet> InitializeRegimeParameters()
    {
        return new Dictionary<MarketRegime, RegimeParameterSet>
        {
            [MarketRegime.Trending] = new RegimeParameterSet
            {
                Regime = MarketRegime.Trending,
                ConfidenceThresholdAdjustment = -0.02, // Slightly more aggressive
                IndicatorAlignmentAdjustment = -1, // Require fewer indicators
                VolumeThresholdMultiplier = 0.8,
                VWAPDeviationAdjustment = 0.02, // Looser VWAP
                Description = "Trending market: More aggressive, looser thresholds"
            },
            
            [MarketRegime.Ranging] = new RegimeParameterSet
            {
                Regime = MarketRegime.Ranging,
                ConfidenceThresholdAdjustment = 0.03, // More conservative
                IndicatorAlignmentAdjustment = 1, // Require more indicators
                VolumeThresholdMultiplier = 1.2,
                VWAPDeviationAdjustment = -0.01, // Tighter VWAP
                Description = "Ranging market: More conservative, tighter thresholds"
            },
            
            [MarketRegime.VolatileTrending] = new RegimeParameterSet
            {
                Regime = MarketRegime.VolatileTrending,
                ConfidenceThresholdAdjustment = 0.05, // Much more conservative
                IndicatorAlignmentAdjustment = 2, // Require many more indicators
                VolumeThresholdMultiplier = 1.5,
                Description = "Volatile trending: Very conservative due to noise"
            },
            
            [MarketRegime.VolatileRanging] = new RegimeParameterSet
            {
                Regime = MarketRegime.VolatileRanging,
                ConfidenceThresholdAdjustment = 0.08, // Most conservative
                IndicatorAlignmentAdjustment = 2,
                VolumeThresholdMultiplier = 2.0,
                Description = "Volatile ranging: Maximum conservatism"
            },
            
            [MarketRegime.CalmTrending] = new RegimeParameterSet
            {
                Regime = MarketRegime.CalmTrending,
                ConfidenceThresholdAdjustment = -0.05, // More aggressive
                IndicatorAlignmentAdjustment = -2, // Fewer indicators needed
                VolumeThresholdMultiplier = 0.6,
                Description = "Calm trending: Aggressive due to clear signals"
            },
            
            [MarketRegime.CalmRanging] = new RegimeParameterSet
            {
                Regime = MarketRegime.CalmRanging,
                ConfidenceThresholdAdjustment = 0.02,
                IndicatorAlignmentAdjustment = 0,
                VolumeThresholdMultiplier = 1.0,
                Description = "Calm ranging: Balanced approach"
            }
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _adjustmentHistory?.Dispose();
            _disposed = true;
            
            _logger.LogInformation("AdaptiveParameterManager disposed");
        }
    }
}

/// <summary>
/// Extension methods for adaptive models
/// </summary>
public static class AdaptiveModelExtensions
{
    /// <summary>
    /// Converts performance metrics to dictionary for storage
    /// </summary>
    public static Dictionary<string, double> ToDictionary(this AdaptivePerformanceMetrics metrics)
    {
        return new Dictionary<string, double>
        {
            ["WinRate"] = metrics.WinRate,
            ["TotalSignals"] = metrics.TotalSignals,
            ["SignalFrequency"] = metrics.SignalFrequency,
            ["AverageConfidence"] = metrics.AverageConfidence,
            ["ProfitFactor"] = metrics.ProfitFactor
        };
    }
}
