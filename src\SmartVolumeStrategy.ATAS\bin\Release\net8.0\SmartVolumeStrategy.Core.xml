<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SmartVolumeStrategy.Core</name>
    </assembly>
    <members>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager">
            <summary>
            Manages dynamic parameter adjustments based on market regime and performance
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.Settings">
            <summary>
            Current adaptive settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.LastAdaptation">
            <summary>
            Time of last parameter adaptation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.AdaptationCount">
            <summary>
            Number of adaptations performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.RecentAdjustments">
            <summary>
            Recent adjustment history
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.SetUserBaseConfiguration(System.Double,System.Int32)">
            <summary>
            Sets the user's base configuration values for adaptive adjustments
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.AdaptToRegimeAsync(SmartVolumeStrategy.Core.Models.MarketRegime,SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics)">
            <summary>
            Adapts parameters based on market regime change
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.AdaptToPerformanceAsync(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics,SmartVolumeStrategy.Core.Models.AdaptationTrigger)">
            <summary>
            Adapts parameters based on performance metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.GetCurrentParameters(System.String)">
            <summary>
            Gets current parameter values for a specific component
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.ApplyRegimeParameters(SmartVolumeStrategy.Core.Models.MarketRegime,SmartVolumeStrategy.Core.Models.RegimeParameterSet,SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics)">
            <summary>
            Applies regime-specific parameter adjustments
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.ApplyPerformanceAdjustments(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics)">
            <summary>
            Applies performance-based parameter adjustments
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.HandleLowWinRate(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics,SmartVolumeStrategy.Core.Models.AdaptationTrigger)">
            <summary>
            Handles low win rate adaptation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.HandleLowSignalFrequency(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics,SmartVolumeStrategy.Core.Models.AdaptationTrigger)">
            <summary>
            Handles low signal frequency adaptation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.HandlePerformanceDegradation(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics,SmartVolumeStrategy.Core.Models.AdaptationTrigger)">
            <summary>
            Handles performance degradation adaptation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.HandleVolatilitySpike(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics,SmartVolumeStrategy.Core.Models.AdaptationTrigger)">
            <summary>
            Handles volatility spike adaptation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.GetAggressivenessMultiplier">
            <summary>
            Gets aggressiveness multiplier based on user settings
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveParameterManager.InitializeRegimeParameters">
            <summary>
            Initializes regime-specific parameter sets
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.AdaptiveModelExtensions">
            <summary>
            Extension methods for adaptive models
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveModelExtensions.ToDictionary(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics)">
            <summary>
            Converts performance metrics to dictionary for storage
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor">
            <summary>
            Monitors strategy performance for adaptive parameter management
            Tracks rolling performance metrics and triggers adaptation events
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.AdaptationTriggered">
            <summary>
            Event fired when adaptation is triggered by performance metrics
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.PerformanceUpdated">
            <summary>
            Event fired when performance metrics are updated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.CurrentMetrics">
            <summary>
            Current performance metrics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.SignalCount">
            <summary>
            Number of signals in current window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.TradeCount">
            <summary>
            Number of completed trades in current window
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.RecordSignal(SmartVolumeStrategy.Core.Models.TradingSignal)">
            <summary>
            Records a new trading signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.RecordTradeResult(SmartVolumeStrategy.Core.Adaptive.TradeResult)">
            <summary>
            Records a completed trade result
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.EvaluateCurrentPerformance">
            <summary>
            Forces immediate performance evaluation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.UpdatePerformanceMetrics">
            <summary>
            Updates performance metrics and checks for adaptation triggers
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.CalculateMetrics(System.DateTime,System.DateTime,System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.TradingSignal},System.Collections.Generic.List{SmartVolumeStrategy.Core.Adaptive.TradeResult})">
            <summary>
            Calculates performance metrics for the given window
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.CheckAdaptationTriggers(SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics,SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics)">
            <summary>
            Checks for conditions that should trigger parameter adaptation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.EvaluatePerformance(System.Object)">
            <summary>
            Periodic evaluation callback
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.CalculateBufferSize">
            <summary>
            Calculates appropriate buffer size based on settings
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptivePerformanceMonitor.CreateEmptyMetrics">
            <summary>
            Creates empty metrics for initialization
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.TradeResult">
            <summary>
            Represents a completed trade result for performance tracking
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.EntryTime">
            <summary>
            Trade entry time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.ExitTime">
            <summary>
            Trade exit time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.Duration">
            <summary>
            Trade duration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.PnL">
            <summary>
            Profit/Loss amount
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.EntryPrice">
            <summary>
            Entry price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.ExitPrice">
            <summary>
            Exit price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.PositionSize">
            <summary>
            Position size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.SignalConfidence">
            <summary>
            Signal confidence that generated this trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.Direction">
            <summary>
            Trade direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.TradeResult.ExitReason">
            <summary>
            Exit reason
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator">
            <summary>
            Coordinates all adaptive system components for intelligent parameter management
            Integrates regime detection, parameter adaptation, performance monitoring, and validation
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.SystemAdapted">
            <summary>
            Event fired when system adapts parameters
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.StatusChanged">
            <summary>
            Event fired when system status changes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.CurrentStatus">
            <summary>
            Current system status
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.IsEnabled">
            <summary>
            Whether adaptive system is enabled
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.ProcessMarketDataAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Processes new market data through the adaptive system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.RecordTradeResult(SmartVolumeStrategy.Core.Adaptive.TradeResult)">
            <summary>
            Records a completed trade result for performance tracking
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.ForceAdaptationAsync(System.String)">
            <summary>
            Forces immediate system evaluation and adaptation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.HandleRegimeChange(SmartVolumeStrategy.Core.Models.MarketRegime)">
            <summary>
            Handles regime changes and triggers adaptation if needed
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.OnAdaptationTriggered(System.Object,SmartVolumeStrategy.Core.Models.AdaptationTrigger)">
            <summary>
            Handles adaptation triggers from performance monitor
            PHASE 3 ENHANCEMENT: Now includes trigger monitoring and validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.OnPerformanceUpdated(System.Object,SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics)">
            <summary>
            Handles performance updates
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.HandleAdaptationTrigger(SmartVolumeStrategy.Core.Models.AdaptationTrigger,SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics)">
            <summary>
            Handles adaptation trigger execution
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.CoordinateSystem(System.Object)">
            <summary>
            Periodic system coordination
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.GetCurrentStatus">
            <summary>
            Gets current system status
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.GetTriggerMonitoringReport">
            <summary>
            PHASE 3: Gets comprehensive adaptive trigger monitoring report
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.ValidateUltraLowTimeframeSettingsAsync(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            PHASE 3: Validates current adaptive settings for ultra-low timeframe appropriateness
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.PerformPhase3AnalysisAsync(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            PHASE 3: Forces a comprehensive system analysis and optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.AnalyzeTriggerEffectiveness(SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport)">
            <summary>
            PHASE 3: Analyzes trigger effectiveness across all trigger types
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.CalculateOverallSystemHealth(SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            PHASE 3: Calculates overall system health score
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.GeneratePhase3Recommendations(SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport,System.Double)">
            <summary>
            PHASE 3: Generates comprehensive recommendations for system optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveSystemCoordinator.GetCurrentMarketData">
            <summary>
            PHASE 3: Gets current market data (placeholder - would need actual implementation)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.AdaptationEvent">
            <summary>
            Represents an adaptation event in the system
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor">
            <summary>
            Comprehensive monitoring and verification system for adaptive triggers
            Ensures triggers are appropriate for ultra-low timeframe trading and working correctly
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.RecordTriggerEvent(SmartVolumeStrategy.Core.Models.AdaptationTrigger,SmartVolumeStrategy.Core.Models.MarketData,System.String)">
            <summary>
            Records a trigger event for monitoring and analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.RecordAdaptationEvent(SmartVolumeStrategy.Core.Adaptive.AdaptationEvent)">
            <summary>
            Records an adaptation event and its outcome
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.VerifyTriggerAppropriateness(SmartVolumeStrategy.Core.Models.TriggerEvent)">
            <summary>
            Verifies if triggers are appropriate for ultra-low timeframe trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.AnalyzeAdaptationEffectiveness(SmartVolumeStrategy.Core.Adaptive.AdaptationEvent)">
            <summary>
            Analyzes the effectiveness of adaptations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.CalculateAdaptationEffectiveness(SmartVolumeStrategy.Core.Models.AdaptationTriggerType,System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.TriggerEvent})">
            <summary>
            Calculates adaptation effectiveness score
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.GetMonitoringReport">
            <summary>
            Gets comprehensive monitoring report
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.AnalyzeUltraLowTimeframeCompatibility">
            <summary>
            Analyzes compatibility with ultra-low timeframe trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.IsTriggerAppropriate(SmartVolumeStrategy.Core.Models.TriggerEvent)">
            <summary>
            Determines if a trigger is appropriate for ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.IdentifyCompatibilityIssues(System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.TriggerEvent})">
            <summary>
            Identifies compatibility issues with ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.CalculateOverallEffectiveness">
            <summary>
            Calculates overall system effectiveness
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.GenerateRecommendations">
            <summary>
            Generates optimization recommendations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.IsUltraLowTimeframe(System.TimeSpan)">
            <summary>
            Determines if evaluation window is appropriate for ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.InitializeTriggerStatistics">
            <summary>
            Initializes trigger statistics tracking
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.AdaptiveTriggerMonitor.UpdateTriggerStatistics(SmartVolumeStrategy.Core.Models.AdaptationTriggerType,SmartVolumeStrategy.Core.Models.TriggerEvent)">
            <summary>
            Updates statistics for a trigger type
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector">
            <summary>
            Detects market regime for adaptive parameter optimization
            Implements volatility, trend, and volume analysis for regime classification
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CurrentRegime">
            <summary>
            Current detected market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.VolatilityScore">
            <summary>
            Current volatility score (0-2.0 range)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.TrendScore">
            <summary>
            Current trend strength score (0-1.0 range)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.VolumeScore">
            <summary>
            Current volume pattern score (0-1.0 range)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.LastUpdate">
            <summary>
            Time of last regime update
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.UpdateMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates market data and recalculates regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.DetectCurrentRegime">
            <summary>
            Detects current market regime based on multi-dimensional analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CalculateVolatilityScore">
            <summary>
            Calculates volatility score based on recent vs baseline ATR
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CalculateTrendScore">
            <summary>
            Calculates trend strength score based on EMA alignment and consistency
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CalculateVolumeScore">
            <summary>
            Calculates volume pattern score based on recent volume activity
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.ClassifyRegime(System.Double,System.Double,System.Double)">
            <summary>
            Classifies market regime based on component scores
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CalculateATR(System.Int32)">
            <summary>
            Calculates Average True Range for specified period
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CalculateCurrentATR">
            <summary>
            Calculates current ATR value for the latest tick
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CalculateTrendConsistency">
            <summary>
            Calculates trend consistency over recent periods
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.MarketRegimeDetector.CalculateAverageVolume(System.Int32)">
            <summary>
            Calculates average volume for specified period
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.EMA">
            <summary>
            Simple Exponential Moving Average implementation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator">
            <summary>
            Validates and optimizes adaptive system settings for ultra-low timeframe trading
            Ensures adaptive triggers are appropriate for 15s/30s/1min scalping strategies
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.ValidateAdaptiveSettingsAsync(SmartVolumeStrategy.Core.Models.AdaptiveSettings,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Validates current adaptive settings for ultra-low timeframe appropriateness
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.ValidateAggressivenessLevel(SmartVolumeStrategy.Core.Models.AdaptiveSettings,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            Validates aggressiveness level for ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.ValidateLearningSensitivity(SmartVolumeStrategy.Core.Models.AdaptiveSettings,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            Validates learning sensitivity for ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.ValidateWinRateThreshold(SmartVolumeStrategy.Core.Models.AdaptiveSettings,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            Validates win rate threshold for ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.ValidatePerformanceWindow(SmartVolumeStrategy.Core.Models.AdaptiveSettings,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            Validates performance window for ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.ValidateMaxParameterAdjustment(SmartVolumeStrategy.Core.Models.AdaptiveSettings,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            Validates maximum parameter adjustment for ultra-low timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.ValidateTriggerAppropriateness(SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            Validates trigger appropriateness using the trigger monitor
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.CalculateOverallScore(System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.AdaptiveValidationResult})">
            <summary>
            Calculates overall validation score
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.GenerateOptimizedSettings(SmartVolumeStrategy.Core.Models.AdaptiveSettings,SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport)">
            <summary>
            Generates optimized settings based on validation results
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.GetCachedValidationReport">
            <summary>
            Gets cached validation report if available
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeAdaptiveValidator.InitializeRecommendedThresholds">
            <summary>
            Initializes recommended thresholds for ultra-low timeframes
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Adaptive.UltraLowTimeframeThresholds">
            <summary>
            Recommended thresholds for ultra-low timeframe trading
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker">
            <summary>
            Tracks indicator performance over time for adaptive selection
            Provides performance-based scoring and trend analysis
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.PerformanceUpdated">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.TrendChanged">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.RecordSignalOutcomeAsync(SmartVolumeStrategy.Core.Models.SignalOutcome)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.RecordSignalOutcome(SmartVolumeStrategy.Core.Models.SignalOutcome)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.GetIndicatorPerformance(System.String)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.GetAllPerformanceData">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.GetPerformanceScore(System.String,SmartVolumeStrategy.Core.Models.TimeWindow,SmartVolumeStrategy.Core.Models.MarketRegime)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.GetTopPerformingIndicators(SmartVolumeStrategy.Core.Models.MarketRegime,SmartVolumeStrategy.Core.Models.TimeWindow,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.GetUnderperformingIndicators(System.Double,SmartVolumeStrategy.Core.Models.TimeWindow)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.UpdatePerformanceMetrics">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.ResetIndicatorPerformance(System.String)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.ResetAllPerformance">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.GetPerformanceSummary">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.ExportPerformanceDataAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.ImportPerformanceDataAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.IndicatorPerformanceTracker.GetTrackerMetrics">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine">
            <summary>
            Advanced performance attribution analysis engine
            Provides detailed analysis of performance drivers and factor attribution
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.AnalyzePerformanceAttributionAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Performs comprehensive performance attribution analysis
            </summary>
            <param name="startDate">Analysis start date</param>
            <param name="endDate">Analysis end date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Performance attribution report</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.AnalyzeIndicatorAttributionAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes performance attribution by indicator contribution
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.AnalyzeRegimeAttributionAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes performance attribution by market regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.AnalyzeTimeAttributionAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes performance attribution by time factors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.AnalyzeConfidenceAttributionAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes performance attribution by confidence levels
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.CalculateRiskAdjustedMetrics(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Calculates risk-adjusted performance metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.DecomposePerformance(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Decomposes performance into contributing factors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.PerformanceAttributionEngine.CompareToBenchmarkAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Compares performance to benchmark metrics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine">
            <summary>
            Automated parameter optimization engine using historical performance data
            Implements grid search, genetic algorithm, and walk-forward analysis
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.MinimumTradesForOptimization">
            <summary>
            Minimum number of trades required for optimization
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.DefaultParameters">
            <summary>
            Default optimization parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.OptimizeParametersAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterDefinition},SmartVolumeStrategy.Core.Analytics.OptimizationParameters,System.Threading.CancellationToken)">
            <summary>
            Performs comprehensive parameter optimization using multiple methods
            </summary>
            <param name="parametersToOptimize">Parameters to optimize</param>
            <param name="optimizationParams">Optimization configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Optimization results</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.PerformGridSearchAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterDefinition},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},System.Threading.CancellationToken)">
            <summary>
            Performs grid search optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.PerformGeneticAlgorithmAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterDefinition},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},SmartVolumeStrategy.Core.Analytics.OptimizationParameters,System.Threading.CancellationToken)">
            <summary>
            Performs genetic algorithm optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.PerformWalkForwardAnalysisAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterDefinition},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},System.Threading.CancellationToken)">
            <summary>
            Performs walk-forward analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.EvaluateParameterSetAsync(System.Collections.Generic.Dictionary{System.String,System.Double},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Evaluates a parameter set against historical trades
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.ShouldTakeTradeWithParameters(SmartVolumeStrategy.Core.Learning.LearningTradeResult,System.Collections.Generic.Dictionary{System.String,System.Double})">
            <summary>
            Determines if a trade should be taken with given parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.GenerateParameterCombinations(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterDefinition})">
            <summary>
            Generates all possible parameter combinations for grid search
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.InitializePopulation(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterDefinition},System.Int32)">
            <summary>
            Initializes population for genetic algorithm
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.CreateNextGeneration(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterSet},SmartVolumeStrategy.Core.Analytics.OptimizationParameters)">
            <summary>
            Creates next generation for genetic algorithm
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.SelectParent(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterSet},System.Random)">
            <summary>
            Selects parent using tournament selection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.Crossover(System.Collections.Generic.Dictionary{System.String,System.Double},System.Collections.Generic.Dictionary{System.String,System.Double},System.Double,System.Random)">
            <summary>
            Performs crossover between two parents
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Analytics.StrategyOptimizationEngine.Mutate(System.Collections.Generic.Dictionary{System.String,System.Double},System.Double,System.Random)">
            <summary>
            Performs mutation on an individual
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.ConfigurationManager">
            <summary>
            Configuration manager with hierarchical configuration and ATAS DataPath integration
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.ConfigurationChanged">
            <summary>
            Event fired when configuration changes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.Current">
            <summary>
            Current configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.Presets">
            <summary>
            Available configuration presets
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.LoadConfigurationAsync(System.String)">
            <summary>
            Loads configuration from file
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.SaveConfigurationAsync(System.String)">
            <summary>
            Saves current configuration to file
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.UpdateConfiguration(SmartVolumeStrategy.Core.Configuration.StrategyConfiguration)">
            <summary>
            Updates configuration with validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.LoadPreset(System.String)">
            <summary>
            Loads a preset configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.SaveAsPresetAsync(System.String,System.String)">
            <summary>
            Saves current configuration as a preset
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.ResetToDefaults">
            <summary>
            Resets configuration to defaults
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.ValidateCurrentConfiguration">
            <summary>
            Validates current configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.GetConfigFilePath(System.String)">
            <summary>
            Gets configuration file path
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.InitializePresets">
            <summary>
            Initializes default presets
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.OnConfigFileChanged(System.Object,System.IO.FileSystemEventArgs)">
            <summary>
            Handles configuration file changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.ConfigurationManager.Dispose">
            <summary>
            Disposes resources
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.ConfigurationChangedEventArgs">
            <summary>
            Configuration changed event arguments
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.IConfigurationManager">
            <summary>
            Interface for configuration management
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.ConfigurationChanged">
            <summary>
            Event fired when configuration changes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.Current">
            <summary>
            Current configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.Presets">
            <summary>
            Available configuration presets
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.LoadConfigurationAsync(System.String)">
            <summary>
            Loads configuration from file
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.SaveConfigurationAsync(System.String)">
            <summary>
            Saves current configuration to file
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.UpdateConfiguration(SmartVolumeStrategy.Core.Configuration.StrategyConfiguration)">
            <summary>
            Updates configuration with validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.LoadPreset(System.String)">
            <summary>
            Loads a preset configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.SaveAsPresetAsync(System.String,System.String)">
            <summary>
            Saves current configuration as a preset
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.ResetToDefaults">
            <summary>
            Resets configuration to defaults
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IConfigurationManager.ValidateCurrentConfiguration">
            <summary>
            Validates current configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations">
            <summary>
            Configuration for all high-probability indicators
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.VolumeFlowIndex">
            <summary>
            Volume Flow Index (5-period MFI) configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.OrderBookPressure">
            <summary>
            Order Book Pressure Ratio configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.VwapDeviation">
            <summary>
            VWAP Deviation Tracker configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.UltraFastRsi">
            <summary>
            Ultra-Fast RSI (3-period) configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.TickMacd">
            <summary>
            Tick-based MACD configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.LargeOrderDetection">
            <summary>
            Large Order Detection configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.MarketRegime">
            <summary>
            Market Regime Detection configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.IndicatorConfigurations.Validate">
            <summary>
            Validates all indicator configurations
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.VolumeFlowIndexConfig">
            <summary>
            Volume Flow Index configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VolumeFlowIndexConfig.Period">
            <summary>
            Period for MFI calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VolumeFlowIndexConfig.OverboughtThreshold">
            <summary>
            Overbought threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VolumeFlowIndexConfig.OversoldThreshold">
            <summary>
            Oversold threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VolumeFlowIndexConfig.Weight">
            <summary>
            Weight in signal calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VolumeFlowIndexConfig.IsEnabled">
            <summary>
            Whether indicator is enabled
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.OrderBookPressureConfig">
            <summary>
            Order Book Pressure Ratio configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.OrderBookPressureConfig.DepthLevels">
            <summary>
            Number of depth levels to analyze
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.OrderBookPressureConfig.BuyingPressureThreshold">
            <summary>
            Strong buying pressure threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.OrderBookPressureConfig.SellingPressureThreshold">
            <summary>
            Strong selling pressure threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.OrderBookPressureConfig.Weight">
            <summary>
            Weight in signal calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.OrderBookPressureConfig.IsEnabled">
            <summary>
            Whether indicator is enabled
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.VwapDeviationConfig">
            <summary>
            VWAP Deviation Tracker configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VwapDeviationConfig.TickPeriod">
            <summary>
            Number of ticks for VWAP calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VwapDeviationConfig.DeviationThreshold">
            <summary>
            Deviation threshold percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VwapDeviationConfig.Weight">
            <summary>
            Weight in signal calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.VwapDeviationConfig.IsEnabled">
            <summary>
            Whether indicator is enabled
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.UltraFastRsiConfig">
            <summary>
            Ultra-Fast RSI configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.UltraFastRsiConfig.Period">
            <summary>
            Period for RSI calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.UltraFastRsiConfig.OverboughtThreshold">
            <summary>
            Overbought threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.UltraFastRsiConfig.OversoldThreshold">
            <summary>
            Oversold threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.UltraFastRsiConfig.Weight">
            <summary>
            Weight in signal calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.UltraFastRsiConfig.IsEnabled">
            <summary>
            Whether indicator is enabled
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.TickMacdConfig">
            <summary>
            Tick-based MACD configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TickMacdConfig.FastPeriod">
            <summary>
            Fast EMA period
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TickMacdConfig.SlowPeriod">
            <summary>
            Slow EMA period
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TickMacdConfig.SignalPeriod">
            <summary>
            Signal line period
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TickMacdConfig.Weight">
            <summary>
            Weight in signal calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TickMacdConfig.IsEnabled">
            <summary>
            Whether indicator is enabled
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.LargeOrderDetectionConfig">
            <summary>
            Large Order Detection configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.LargeOrderDetectionConfig.SizeMultiplier">
            <summary>
            Multiplier for average trade size to detect large orders
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.LargeOrderDetectionConfig.AveragePeriod">
            <summary>
            Period for calculating average trade size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.LargeOrderDetectionConfig.Weight">
            <summary>
            Weight in signal calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.LargeOrderDetectionConfig.IsEnabled">
            <summary>
            Whether indicator is enabled
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.MarketRegimeConfig">
            <summary>
            Market Regime Detection configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.MarketRegimeConfig.VolatilityPeriod">
            <summary>
            Volatility calculation period
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.MarketRegimeConfig.TrendPeriod">
            <summary>
            Trend detection period
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.MarketRegimeConfig.HighVolatilityThreshold">
            <summary>
            High volatility threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.MarketRegimeConfig.LowVolatilityThreshold">
            <summary>
            Low volatility threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.MarketRegimeConfig.Weight">
            <summary>
            Weight in signal calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.MarketRegimeConfig.IsEnabled">
            <summary>
            Whether indicator is enabled
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.AdaptiveParameterConfig">
            <summary>
            Adaptive parameter configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AdaptiveParameterConfig.IsEnabled">
            <summary>
            Whether adaptive parameters are enabled
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AdaptiveParameterConfig.AdaptationFrequencyMinutes">
            <summary>
            Adaptation frequency in minutes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AdaptiveParameterConfig.LearningRate">
            <summary>
            Learning rate for parameter adjustment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AdaptiveParameterConfig.MinSamplesForAdaptation">
            <summary>
            Minimum performance samples for adaptation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AdaptiveParameterConfig.MaxAdjustmentPercent">
            <summary>
            Maximum parameter adjustment percentage
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration">
            <summary>
            Hierarchical configuration system with ATAS DataPath integration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Version">
            <summary>
            Configuration version for compatibility checking
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.General">
            <summary>
            General strategy settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Signals">
            <summary>
            Signal generation settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Validation">
            <summary>
            Validation engine settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.RiskManagement">
            <summary>
            Risk management settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Performance">
            <summary>
            Performance monitoring settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Atas">
            <summary>
            ATAS-specific settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Indicators">
            <summary>
            Indicator-specific configurations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Metadata">
            <summary>
            Configuration metadata
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Validate">
            <summary>
            Validates the entire configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Configuration.StrategyConfiguration.Clone">
            <summary>
            Creates a deep copy of the configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.GeneralSettings">
            <summary>
            General strategy settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.StrategyName">
            <summary>
            Strategy name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.PrimaryTimeframe">
            <summary>
            Primary timeframe for analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.SecondaryTimeframe">
            <summary>
            Secondary timeframe for confirmation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.IsEnabled">
            <summary>
            Whether strategy is enabled
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.MaxConcurrentPositions">
            <summary>
            Maximum number of concurrent positions
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.SessionFilter">
            <summary>
            Trading session filter
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.GeneralSettings.LogLevel">
            <summary>
            Logging level
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.SignalSettings">
            <summary>
            Signal generation settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.SignalSettings.MinConfidenceThreshold">
            <summary>
            Minimum confidence threshold for signal generation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.SignalSettings.MinIndicatorAlignment">
            <summary>
            Minimum number of indicators that must align
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.SignalSettings.SignalExpirationMinutes">
            <summary>
            Signal expiration time in minutes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.SignalSettings.MaxSignalsPerHour">
            <summary>
            Maximum signals per hour
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.SignalSettings.TargetProfitPercent">
            <summary>
            Target profit percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.SignalSettings.StopLossPercent">
            <summary>
            Stop loss percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.SignalSettings.UseAdaptiveThresholds">
            <summary>
            Whether to use adaptive thresholds
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.ValidationSettings">
            <summary>
            Validation engine settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ValidationSettings.IsEnabled">
            <summary>
            Whether validation is enabled
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ValidationSettings.MaxValidationTimeMs">
            <summary>
            Maximum validation time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ValidationSettings.UseParallelValidation">
            <summary>
            Whether to use parallel validation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ValidationSettings.RulePriorities">
            <summary>
            Validation rule priorities
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.RiskManagementSettings">
            <summary>
            Risk management settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.RiskManagementSettings.MaxPositionSize">
            <summary>
            Maximum position size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.RiskManagementSettings.MaxDailyLossPercent">
            <summary>
            Maximum daily loss percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.RiskManagementSettings.MaxDrawdownPercent">
            <summary>
            Maximum drawdown percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.RiskManagementSettings.SizingMethod">
            <summary>
            Position sizing method
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.RiskManagementSettings.RiskPerTradePercent">
            <summary>
            Risk per trade percentage
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.PerformanceSettings">
            <summary>
            Performance monitoring settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.PerformanceSettings.IsEnabled">
            <summary>
            Whether performance monitoring is enabled
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.PerformanceSettings.MetricsIntervalSeconds">
            <summary>
            Metrics collection interval in seconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.PerformanceSettings.MaxHistoryDays">
            <summary>
            Maximum metrics history days
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.PerformanceSettings.ExportToFile">
            <summary>
            Whether to export metrics to file
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.PerformanceSettings.ExportFormat">
            <summary>
            Export file format
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.AtasSettings">
            <summary>
            ATAS-specific settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AtasSettings.DataPath">
            <summary>
            ATAS data path for file operations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AtasSettings.MaxLatencyMs">
            <summary>
            Maximum processing latency in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AtasSettings.MemoryLimitMB">
            <summary>
            Memory usage limit in MB
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AtasSettings.CpuLimitPercent">
            <summary>
            CPU usage limit percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.AtasSettings.UseAtasLogging">
            <summary>
            Whether to use ATAS logging
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.TradingSessionFilter">
            <summary>
            Trading session filter
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TradingSessionFilter.AllowedSessions">
            <summary>
            Allowed trading sessions
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TradingSessionFilter.StartTime">
            <summary>
            Trading start time (UTC)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TradingSessionFilter.EndTime">
            <summary>
            Trading end time (UTC)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.TradingSessionFilter.TradeWeekends">
            <summary>
            Whether to trade on weekends
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.PositionSizingMethod">
            <summary>
            Position sizing methods
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.LogLevel">
            <summary>
            Log level enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Configuration.ConfigurationMetadata">
            <summary>
            Configuration metadata
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ConfigurationMetadata.CreatedAt">
            <summary>
            Configuration creation time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ConfigurationMetadata.ModifiedAt">
            <summary>
            Last modification time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ConfigurationMetadata.Author">
            <summary>
            Configuration author
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ConfigurationMetadata.Description">
            <summary>
            Configuration description
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Configuration.ConfigurationMetadata.Tags">
            <summary>
            Configuration tags
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.BaseIndicator">
            <summary>
            Base implementation for all trading indicators with performance monitoring and ATAS compatibility
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Logger">
            <summary>
            Protected access to logger for derived classes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Id">
            <inheritdoc />
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Name">
            <inheritdoc />
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Tier">
            <inheritdoc />
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Weight">
            <inheritdoc />
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.IsEnabled">
            <inheritdoc />
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.IsReady">
            <inheritdoc />
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.BaseIndicator.LastProcessingTimeMs">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.Core.Indicators.BaseIndicator.ReadingUpdated">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.Core.Indicators.BaseIndicator.PerformanceUpdated">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Update(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Reset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Abstract method for indicator-specific calculation logic
            </summary>
            <param name="marketData">Market data to process</param>
            <returns>Calculated indicator reading</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.OnReset">
            <summary>
            Abstract method for indicator-specific reset logic
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.CreateBuffer``1(System.Int32)">
            <summary>
            Creates a circular buffer for efficient data storage
            </summary>
            <typeparam name="T">Buffer data type</typeparam>
            <param name="capacity">Buffer capacity</param>
            <returns>Circular buffer instance</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.ValidateRange(System.Decimal,System.Decimal,System.Decimal,System.String)">
            <summary>
            Validates that a value is within expected range
            </summary>
            <param name="value">Value to validate</param>
            <param name="min">Minimum allowed value</param>
            <param name="max">Maximum allowed value</param>
            <param name="parameterName">Parameter name for logging</param>
            <returns>True if valid</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.CalculateEMA(System.Decimal,System.Decimal,System.Int32)">
            <summary>
            Calculates exponential moving average
            </summary>
            <param name="currentValue">Current value</param>
            <param name="previousEma">Previous EMA value</param>
            <param name="period">EMA period</param>
            <returns>New EMA value</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.CalculateSMA(SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer{System.Decimal})">
            <summary>
            Calculates simple moving average from buffer
            </summary>
            <param name="buffer">Data buffer</param>
            <returns>Simple moving average</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.BaseIndicator.Dispose">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager">
            <summary>
            Adaptive Parameter Manager - Dynamically adjusts indicator parameters based on market conditions
            Provides 20-30% better signal accuracy by optimizing parameters for current market state
            Target: Less than 2ms parameter adjustment, real-time market condition analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.UpdateMarketConditions(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates market conditions and adapts parameters if needed
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.GetParameters(System.String)">
            <summary>
            Gets adaptive parameters for a specific indicator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.CalculateMarketConditions(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Calculates current market conditions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.AdaptAllParameters(SmartVolumeStrategy.Core.Indicators.Enhanced.MarketConditions)">
            <summary>
            Adapts parameters for all indicators based on market conditions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.ApplyAdaptationRule(SmartVolumeStrategy.Core.Indicators.Enhanced.ParameterAdaptationRule,SmartVolumeStrategy.Core.Indicators.Enhanced.MarketConditions)">
            <summary>
            Applies adaptation rule to get new parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.InitializeAdaptationRules">
            <summary>
            Initializes adaptation rules for each indicator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.UpdateHistoryBuffers(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates history buffers with new market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.CalculateVolatility">
            <summary>
            Calculates current volatility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.CalculateVolumeIntensity(System.Int64)">
            <summary>
            Calculates volume intensity
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.CalculateAverageSpread(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Calculates average spread
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.DetermineMarketRegime(System.Decimal,System.Decimal)">
            <summary>
            Determines market regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.CalculateTrendStrength">
            <summary>
            Calculates trend strength
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.HasSignificantChange(SmartVolumeStrategy.Core.Indicators.Enhanced.MarketConditions,SmartVolumeStrategy.Core.Indicators.Enhanced.MarketConditions)">
            <summary>
            Checks if market conditions have changed significantly
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameterManager.GetCurrentConditions">
            <summary>
            Gets current market conditions
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.MarketConditions">
            <summary>
            Market conditions for parameter adaptation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.MarketRegime">
            <summary>
            Market regime types
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.AdaptiveParameters">
            <summary>
            Adaptive parameters for indicators
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.ParameterAdaptationRule">
            <summary>
            Parameter adaptation rule for an indicator
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine">
            <summary>
            Multi-Timeframe Indicator Engine for enhanced signal confluence
            Analyzes indicators across multiple timeframes for higher confidence signals
            Target: 15-25% confidence boost when 3+ timeframes align
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.GenerateConfluenceSignal(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Generates multi-timeframe confluence signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.UpdateAllTimeframes(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates indicators with new market data for all timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.AnalyzeTimeframeConfluence(System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.TimeframeSignal},System.DateTime)">
            <summary>
            Analyzes confluence across timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.CalculateConfluenceMetrics(System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.TimeframeSignal})">
            <summary>
            Calculates confluence metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.ApplyConfluenceBoost(System.Double,SmartVolumeStrategy.Core.Indicators.Enhanced.ConfluenceMetrics)">
            <summary>
            Applies confluence boost to base confidence
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.CalculatePriorityBonus(System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.TimeframeSignal})">
            <summary>
            Calculates priority alignment bonus
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.GetPrimaryTimeframe(System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.TimeframeSignal})">
            <summary>
            Gets the primary timeframe from aligned signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.InitializeTimeframeIndicators">
            <summary>
            Initializes indicator sets for each timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.MultiTimeframeIndicatorEngine.UpdatePerformanceMetrics(System.Double)">
            <summary>
            Updates performance metrics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeConfig">
            <summary>
            Configuration for each timeframe
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.ConfluenceMetrics">
            <summary>
            Confluence analysis metrics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet">
            <summary>
            Manages a complete set of indicators for a specific timeframe
            Optimized for ultra-low timeframe scalping with timeframe-specific parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.UpdateIndicators(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates all indicators with new market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.GenerateSignal(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Generates signal from all indicators in this timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.ConvertToTimeframeData(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Converts tick data to timeframe bars
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.CalculateWeightedSignal(System.Collections.Generic.List{SmartVolumeStrategy.Core.Indicators.IndicatorReading})">
            <summary>
            Calculates weighted signal from multiple indicators
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.GetTimeframeOptimizedConfig(System.TimeSpan)">
            <summary>
            Gets timeframe-optimized configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.GetIndicatorWeights">
            <summary>
            Gets indicator weights for this timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.CalculateBufferSize(System.TimeSpan)">
            <summary>
            Calculates buffer size based on timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.GetBarStartTime(System.DateTime,System.TimeSpan)">
            <summary>
            Gets bar start time for timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorSet.UpdatePerformanceMetrics(System.Double)">
            <summary>
            Updates performance metrics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.TimeframeIndicatorConfig">
            <summary>
            Timeframe-specific indicator configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands">
            <summary>
            Volume-Weighted Bollinger Bands - Enhanced Bollinger Bands with volume weighting
            Provides 15-20% better signal quality by incorporating volume patterns
            Target: Less than 5ms processing, enhanced confidence for high-volume moves
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands},System.Int32,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Initializes Volume-Weighted Bollinger Bands with enhanced volume analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.GetLastReading">
            <summary>
            Gets the last calculated reading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.UpdateIndicator(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates the indicator with new market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.CalculateVolumeWeightedBands">
            <summary>
            Calculates volume-weighted Bollinger Bands
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.CalculateBandPosition(System.Decimal,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Calculates band position (0 = lower band, 0.5 = middle, 1 = upper band)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.CalculateVolumeWeight(System.Int64)">
            <summary>
            Calculates volume weight for current trade
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.DetectInstitutionalActivity(System.Int64,System.Decimal)">
            <summary>
            Detects institutional activity based on volume patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.AnalyzeVolumeConfirmation(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Analyzes volume confirmation for the signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.DetermineVolumeWeightedSignal(System.Decimal,System.Double,SmartVolumeStrategy.Core.Indicators.Enhanced.InstitutionalActivity,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Determines signal with volume weighting considerations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.DetermineBaseSignal(System.Decimal)">
            <summary>
            Determines base signal from band position
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.CalculateBaseConfidence(System.Decimal,SmartVolumeStrategy.Core.Models.SignalDirection)">
            <summary>
            Calculates base confidence from band position
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.ValidateMarketDataWithVolume(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Validates market data with volume requirements
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.UpdateVolumeMetrics(System.Decimal)">
            <summary>
            Updates volume-related performance metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedBollingerBands.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI">
            <summary>
            Volume-Weighted RSI - Enhanced RSI with volume weighting for institutional activity detection
            Provides 10-15% better signal quality by incorporating volume patterns
            Target: Less than 5ms processing, enhanced confidence for high-volume moves
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI},System.Int32,System.Decimal,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Initializes Volume-Weighted RSI with enhanced volume analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.GetLastReading">
            <summary>
            Gets the last calculated reading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.UpdateIndicator(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates the indicator with new market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.CalculateVolumeWeightedRSI">
            <summary>
            Calculates volume-weighted RSI
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.CalculateVolumeWeight(System.Int64)">
            <summary>
            Calculates volume weight for current trade
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.DetectInstitutionalActivity(System.Int64,System.Decimal)">
            <summary>
            Detects institutional activity based on volume patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.AnalyzeVolumeConfirmation(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Analyzes volume confirmation for the signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.DetermineVolumeWeightedSignal(System.Decimal,System.Double,SmartVolumeStrategy.Core.Indicators.Enhanced.InstitutionalActivity)">
            <summary>
            Determines signal with volume weighting considerations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.DetermineBaseSignal(System.Decimal)">
            <summary>
            Determines base signal from RSI value
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.CalculateBaseConfidence(System.Decimal,SmartVolumeStrategy.Core.Models.SignalDirection)">
            <summary>
            Calculates base confidence from RSI value
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.ValidateMarketDataWithVolume(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Validates market data with volume requirements
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.ValidateMarketDataInternal(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Internal market data validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.UpdateVolumeMetrics(System.Decimal)">
            <summary>
            Updates volume-related performance metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Enhanced.VolumeWeightedRSI.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.InstitutionalActivity">
            <summary>
            Institutional activity detection result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Enhanced.InstitutionalActivityType">
            <summary>
            Types of institutional activity
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator">
            <summary>
            High-Probability Signal Generator - Combines all 7 indicators for signal generation
            Requires 5 or more indicators aligned with 0.85 or higher confidence threshold
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator},SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex,SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio,SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker,SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection,SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands,SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator,SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI,SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD,SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis,SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine)">
            <summary>
            Initializes a new High-Probability Signal Generator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="volumeFlowIndex">Volume Flow Index indicator</param>
            <param name="orderBookPressure">Order Book Pressure Ratio indicator</param>
            <param name="vwapDeviation">VWAP Deviation Tracker indicator</param>
            <param name="largeOrderDetection">Large Order Detection indicator</param>
            <param name="bollingerBands">Bollinger Bands indicator</param>
            <param name="stochasticOscillator">Stochastic Oscillator indicator</param>
            <param name="ultraFastRSI">Ultra-Fast RSI indicator</param>
            <param name="tickBasedMACD">Tick-Based MACD indicator</param>
            <param name="liquidityGapAnalysis">Liquidity Gap Analysis indicator</param>
            <param name="selectionEngine">Optional smart indicator selection engine for adaptive indicator selection</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.ConfidenceThreshold">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.HighConfidenceSignalGenerated">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.StatisticsUpdated">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.GenerateSignalAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.GenerateSignalBatchAsync(System.Collections.Generic.IEnumerable{SmartVolumeStrategy.Core.Models.MarketData},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.UpdateMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.SetConfidenceThreshold(System.Double)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.SetIndicatorAlignmentThreshold(System.Int32)">
            <summary>
            Sets the minimum indicator alignment requirement for signal generation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.GetStatistics">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.SetUserPreferences(SmartVolumeStrategy.Core.Models.UserIndicatorPreferences)">
            <summary>
            Configures user preferences for smart indicator selection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.SetSmartSelectionEnabled(System.Boolean)">
            <summary>
            CRITICAL FIX: Enhanced smart indicator selection with better integration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.GetThresholdOptimizationSuggestions">
            <summary>
            CRITICAL DEBUG: Gets threshold optimization suggestions for ultra-low timeframe demo data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.HighProbabilitySignalGenerator.Dispose">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.IIndicator">
            <summary>
            Base interface for all trading indicators in the high-probability suite
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IIndicator.Id">
            <summary>
            Unique identifier for the indicator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IIndicator.Name">
            <summary>
            Human-readable name of the indicator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IIndicator.Tier">
            <summary>
            Indicator tier (1 = Essential, 2 = Important)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IIndicator.Weight">
            <summary>
            Weight of this indicator in signal generation (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IIndicator.IsEnabled">
            <summary>
            Whether the indicator is currently enabled
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IIndicator.IsReady">
            <summary>
            Whether the indicator has enough data to produce valid readings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IIndicator.LastProcessingTimeMs">
            <summary>
            Last processing time in milliseconds
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.IIndicator.Update(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates the indicator with new market data
            </summary>
            <param name="marketData">Latest market data</param>
            <returns>Indicator reading result</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.IIndicator.GetCurrentReading">
            <summary>
            Gets the current indicator reading without updating
            </summary>
            <returns>Current indicator reading</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.IIndicator.Reset">
            <summary>
            Resets the indicator to initial state
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.IIndicator.GetConfiguration">
            <summary>
            Gets indicator-specific configuration parameters
            </summary>
            <returns>Configuration dictionary</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.IIndicator.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Updates indicator configuration parameters
            </summary>
            <param name="configuration">New configuration parameters</param>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Indicators.IIndicator.ReadingUpdated">
            <summary>
            Event fired when a new reading is available
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Indicators.IIndicator.PerformanceUpdated">
            <summary>
            Event fired when indicator performance metrics are updated
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.IndicatorReading">
            <summary>
            Indicator reading with signal information
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.IndicatorName">
            <summary>
            Indicator name that generated this reading
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.Value">
            <summary>
            Primary indicator value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.Direction">
            <summary>
            Signal direction (Buy/Sell/None)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.Confidence">
            <summary>
            Confidence in the signal (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.Timestamp">
            <summary>
            Timestamp when reading was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.IsValid">
            <summary>
            Whether this reading represents a valid signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.AdditionalValues">
            <summary>
            Additional indicator-specific values
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.Metadata">
            <summary>
            Metadata about the reading
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorReading.Strength">
            <summary>
            Signal strength (0.0 to 1.0)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.IndicatorReading.Invalid(System.String)">
            <summary>
            Creates an invalid reading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.IndicatorReading.NoSignal(System.String,System.Decimal)">
            <summary>
            Creates a no-signal reading
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics">
            <summary>
            Performance metrics for indicators
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.IndicatorName">
            <summary>
            Indicator name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.AverageProcessingTimeMs">
            <summary>
            Average processing time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.MaxProcessingTimeMs">
            <summary>
            Maximum processing time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.TotalUpdates">
            <summary>
            Total number of updates processed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.ValidSignals">
            <summary>
            Number of valid signals generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.MemoryUsageBytes">
            <summary>
            Memory usage in bytes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.LastUpdate">
            <summary>
            Last update timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.IndicatorPerformanceMetrics.SignalRate">
            <summary>
            Signal generation rate (signals per minute)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Optimization.IndicatorThresholdOptimizer">
            <summary>
            Indicator Threshold Optimizer for Ultra-Low Timeframe Demo Data
            Analyzes indicator performance and suggests optimal thresholds for 15s/30s/1min charts
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Optimization.IndicatorThresholdOptimizer.AnalyzeIndicatorPerformance(System.String,SmartVolumeStrategy.Core.Indicators.IndicatorReading)">
            <summary>
            Analyzes indicator performance and suggests threshold optimizations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Optimization.IndicatorThresholdOptimizer.GetOptimizationSuggestions">
            <summary>
            Gets optimization suggestions for all indicators
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Optimization.IndicatorStats">
            <summary>
            Statistics tracker for individual indicators
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Optimization.ThresholdSuggestions">
            <summary>
            Threshold optimization suggestions for an indicator
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine">
            <summary>
            Smart Indicator Selection Engine - Intelligently selects indicators based on market conditions and user preferences
            Provides transparent, adaptive indicator selection for optimal signal generation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine.CurrentMarketRegime">
            <inheritdoc />
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine.LastSelectionResult">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine.SelectionChanged">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine.SelectIndicatorsAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Collections.Generic.List{SmartVolumeStrategy.Core.Indicators.IndicatorReading},System.Int32,SmartVolumeStrategy.Core.Models.UserIndicatorPreferences)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine.SelectIndicators(SmartVolumeStrategy.Core.Models.MarketData,System.Collections.Generic.List{SmartVolumeStrategy.Core.Indicators.IndicatorReading},System.Int32,SmartVolumeStrategy.Core.Models.UserIndicatorPreferences)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine.UpdateMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.SmartIndicatorSelectionEngine.GetPerformanceMetrics">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands">
            <summary>
            Bollinger Bands - Tier 1 Indicator
            Volatility-based mean reversion system for detecting price extremes and squeeze/expansion patterns
            Target: Less than 5ms processing time, 20-period SMA with 2 standard deviation bands
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands},System.Int32,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Initializes a new Bollinger Bands indicator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="period">Period for moving average calculation (default: 20)</param>
            <param name="standardDeviations">Number of standard deviations for bands (default: 2.0)</param>
            <param name="squeezeThreshold">Bandwidth threshold for squeeze detection (default: 0.1)</param>
            <param name="expansionThreshold">Bandwidth threshold for expansion detection (default: 0.25)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.BollingerBands.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection">
            <summary>
            Large Order Detection - Tier 1 Indicator
            Whale activity identification through trade size analysis
            Target: 5x+ average trade size detection, 100-trade rolling average
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection},System.Int32,System.Decimal)">
            <summary>
            Initializes a new Large Order Detection indicator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="averagePeriod">Period for calculating average trade size (default: 100)</param>
            <param name="sizeMultiplier">Multiplier for large trade threshold (default: 5.0)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.LargeOrderDetection.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio">
            <summary>
            Order Book Pressure Ratio - Tier 1 Indicator
            Real-time bid/ask imbalance detection for immediate pressure identification
            Target: Less than 5ms processing time, greater than 2.0 buying pressure, less than 0.5 selling pressure
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio},System.Int32,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new Order Book Pressure Ratio indicator
            CRITICAL FIX: Balanced thresholds to eliminate SELL bias
            </summary>
            <param name="logger">Logger instance</param>
            <param name="levels">Number of order book levels to analyze (default: 5)</param>
            <param name="buyThreshold">Threshold for buy pressure signals (BALANCED: reduced from 2.0 to 1.3)</param>
            <param name="sellThreshold">Threshold for sell pressure signals (BALANCED: increased from 0.5 to 0.7)</param>
            <param name="momentumThreshold">Minimum momentum for signal confirmation (default: 0.1)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.OrderBookPressureRatio.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator">
            <summary>
            Stochastic Oscillator - Tier 1 Indicator
            Momentum oscillator comparing closing price to price range over time for overbought/oversold detection
            Target: Less than 5ms processing time, %K (14-period) and %D (3-period SMA) with crossover signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator},System.Int32,System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Initializes a new Stochastic Oscillator indicator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="kPeriod">Period for %K calculation (default: 14)</param>
            <param name="dPeriod">Period for %D smoothing (default: 3)</param>
            <param name="overboughtThreshold">Overbought threshold (default: 80)</param>
            <param name="oversoldThreshold">Oversold threshold (default: 20)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.StochasticOscillator.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex">
            <summary>
            Volume Flow Index (3-Period MFI) - Tier 1 Indicator - OPTIMIZED FOR ULTRA-LOW TIMEFRAMES
            Detects immediate buying/selling pressure for 0.5% moves on 15s-1min charts
            Target: Less than 5ms processing time, 70+ strong buying, 30- strong selling
            Enhanced with momentum detection and institutional flow recognition
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex},System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Initializes a new Volume Flow Index indicator - OPTIMIZED FOR SCALPING
            CRITICAL FIX: Balanced thresholds to reduce SELL signal frequency bias
            </summary>
            <param name="logger">Logger instance</param>
            <param name="period">Period for MFI calculation (default: 3 for ultra-fast response)</param>
            <param name="buyThreshold">Threshold for buy signals (BALANCED: adjusted for symmetry)</param>
            <param name="sellThreshold">Threshold for sell signals (BALANCED: adjusted for symmetry)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.DetermineEnhancedSignal(System.Decimal,System.Decimal)">
            <summary>
            ENHANCED: Determines signal with momentum and institutional flow detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.CalculateMFIMomentum">
            <summary>
            Calculates MFI momentum for trend detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.CalculateVolumeIntensity(System.Decimal)">
            <summary>
            Calculates volume intensity multiplier
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VolumeFlowIndex.ValidateMarketDataEnhanced(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Enhanced market data validation with demo data compatibility
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker">
            <summary>
            VWAP Deviation Tracker - Tier 1 Indicator
            Institutional reference point analysis for detecting significant price deviations
            Target: Less than 5ms processing time, plus or minus 0.1% deviation thresholds
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker},System.Int32,System.Decimal)">
            <summary>
            Initializes a new VWAP Deviation Tracker indicator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="period">Period for VWAP calculation (default: 14)</param>
            <param name="deviationThreshold">Deviation threshold as percentage (default: 0.1%)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier1.VWAPDeviationTracker.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis">
            <summary>
            Liquidity Gap Analysis - Tier 2 Indicator
            Path of least resistance mapping for 0.5% moves
            Target: Plus or minus 0.5% price range analysis, less than 50% average volume threshold
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis},System.Decimal,System.Decimal,System.Int32)">
            <summary>
            Initializes a new Liquidity Gap Analysis indicator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="priceRangePercent">Price range to analyze as percentage (default: 0.5%)</param>
            <param name="volumeThresholdPercent">Volume threshold as percentage of average (default: 50%)</param>
            <param name="volumeAveragePeriod">Period for volume average calculation (default: 50)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.LiquidityGapAnalysis.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD">
            <summary>
            Tick-Based MACD - Tier 2 Indicator
            Micro-trend confirmation system using exponential moving averages
            Target: 12/26/9-tick EMAs, signal line crossovers, momentum analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD},System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new Tick-Based MACD indicator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="fastPeriod">Fast EMA period (default: 12)</param>
            <param name="slowPeriod">Slow EMA period (default: 26)</param>
            <param name="signalPeriod">Signal line EMA period (default: 9)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.TickBasedMACD.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI">
            <summary>
            Ultra-Fast RSI (3-Period) - Tier 2 Indicator
            Momentum extremes for reversal detection in ultra-low timeframes
            Target: Less than 5ms processing time, greater than 65 overbought, less than 35 oversold
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI},System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Initializes a new Ultra-Fast RSI indicator
            </summary>
            <param name="logger">Logger instance</param>
            <param name="period">Period for RSI calculation (default: 3)</param>
            <param name="overboughtThreshold">Overbought threshold (default: 65)</param>
            <param name="oversoldThreshold">Oversold threshold (default: 35)</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI.IsReady">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI.CalculateReading(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI.GetCurrentReading">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI.OnReset">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI.GetConfiguration">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.Core.Indicators.Tier2.UltraFastRSI.UpdateConfiguration(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions">
            <summary>
            Extension methods for registering adaptive system services
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.AddAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection,SmartVolumeStrategy.Core.Models.AdaptiveSettings)">
            <summary>
            Adds the complete adaptive system to the service collection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.AddAdaptiveCore(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds core adaptive system components
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.AddValidationSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds hierarchical validation system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.AddValidationRules(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds validation rules to the service collection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.AddAdaptiveCoordination(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds adaptive system coordination
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.AddAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder})">
            <summary>
            Adds adaptive system with custom configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.AddRiskManagement(Microsoft.Extensions.DependencyInjection.IServiceCollection,SmartVolumeStrategy.Core.Validation.Rules.RiskParameters)">
            <summary>
            Adds risk management parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveServiceExtensions.ValidateAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Validates adaptive system registration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder">
            <summary>
            Builder for configuring the adaptive system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.WithSettings(System.Action{SmartVolumeStrategy.Core.Models.AdaptiveSettings})">
            <summary>
            Configures adaptive settings
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.WithRiskParameters(System.Func{SmartVolumeStrategy.Core.Validation.Rules.RiskParameters})">
            <summary>
            Configures risk parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.WithAggressiveness(System.Int32)">
            <summary>
            Sets aggressiveness level
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.WithAutoAdaptation(System.Boolean)">
            <summary>
            Enables or disables auto-adaptation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.WithLearningSensitivity(System.Double)">
            <summary>
            Sets learning sensitivity
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.WithMinWinRateThreshold(System.Double)">
            <summary>
            Sets minimum win rate threshold
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.AddValidationRule``1">
            <summary>
            Adds custom validation rule
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.AddValidationRule``1(System.Func{System.IServiceProvider,``0})">
            <summary>
            Adds custom validation rule with factory
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveSystemBuilder.Build">
            <summary>
            Builds the adaptive system configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveScenarioExtensions">
            <summary>
            Configuration extensions for specific scenarios
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveScenarioExtensions.AddConservativeAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures adaptive system for conservative trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveScenarioExtensions.AddAggressiveAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures adaptive system for aggressive trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveScenarioExtensions.AddBalancedAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures adaptive system for balanced trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveScenarioExtensions.AddCryptoAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures adaptive system for crypto trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.AdaptiveScenarioExtensions.AddForexAdaptiveSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures adaptive system for forex trading
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorServiceExtensions">
            <summary>
            Extension methods for registering indicator services with dependency injection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorServiceExtensions.AddIndicatorServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers all indicator services with the service collection
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorServiceExtensions.AddIndicatorServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorConfiguration})">
            <summary>
            Registers indicator services with custom configuration
            </summary>
            <param name="services">Service collection</param>
            <param name="configureIndicators">Action to configure indicators</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorServiceExtensions.ValidateIndicatorServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Validates that all required indicator services are registered
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorServiceExtensions.AddProductionServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers production monitoring and deployment services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorConfiguration">
            <summary>
            Configuration class for indicator parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.IndicatorConfiguration.IsValid">
            <summary>
            Validates the configuration parameters
            </summary>
            <returns>True if configuration is valid</returns>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.ServiceCollectionExtensions">
            <summary>
            Extension methods for configuring services in the DI container
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.ServiceCollectionExtensions.AddSmartVolumeStrategy(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Adds core strategy services to the DI container
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.ServiceCollectionExtensions.AddStrategyLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds basic logging configuration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.ServiceCollectionExtensions.AddSignalGeneration(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds signal generation services with smart indicator selection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.ServiceCollectionExtensions.AddSmartIndicatorSelection(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds smart indicator selection services with advanced features
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.DependencyInjection.ServiceCollectionExtensions.ValidateRegistration(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Validates service registration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker">
            <summary>
            Circuit breaker pattern implementation for fault tolerance
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.State">
            <summary>
            Current circuit breaker state
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.FailureCount">
            <summary>
            Current failure count
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.StateChanged">
            <summary>
            Event fired when circuit breaker state changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.ExecuteAsync``1(System.Func{System.Threading.Tasks.Task{``0}},System.String)">
            <summary>
            Executes an operation with circuit breaker protection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.Execute``1(System.Func{``0},System.String)">
            <summary>
            Executes a synchronous operation with circuit breaker protection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.ExecuteAsync(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Executes an operation without return value
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.CanExecute">
            <summary>
            Checks if operation can be executed
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.OnSuccess">
            <summary>
            Handles successful operation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.OnFailure(System.Exception,System.String)">
            <summary>
            Handles failed operation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.ChangeState(SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerState)">
            <summary>
            Changes circuit breaker state
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.Reset">
            <summary>
            Manually resets the circuit breaker
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreaker.GetStatistics">
            <summary>
            Gets circuit breaker statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerState">
            <summary>
            Circuit breaker states
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerStateChangedEventArgs">
            <summary>
            Circuit breaker state changed event arguments
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerStatistics">
            <summary>
            Circuit breaker statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerOpenException">
            <summary>
            Exception thrown when circuit breaker is open
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerManager">
            <summary>
            Circuit breaker manager for managing multiple circuit breakers
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerManager.GetCircuitBreaker(System.String,System.Int32,System.Nullable{System.TimeSpan})">
            <summary>
            Gets or creates a circuit breaker for the specified key
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerManager.ExecuteAsync``1(System.String,System.Func{System.Threading.Tasks.Task{``0}},System.Int32,System.Nullable{System.TimeSpan})">
            <summary>
            Executes an operation with circuit breaker protection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerManager.GetAllStatistics">
            <summary>
            Gets statistics for all circuit breakers
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerManager.ResetAll">
            <summary>
            Resets all circuit breakers
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.CircuitBreakerManager.RemoveCircuitBreaker(System.String)">
            <summary>
            Removes a circuit breaker
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicy">
            <summary>
            Retry policy with exponential backoff for resilient operations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicy.ExecuteAsync``1(System.Func{System.Threading.Tasks.Task{``0}},System.String)">
            <summary>
            Executes an async operation with retry logic
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicy.Execute``1(System.Func{``0},System.String)">
            <summary>
            Executes a synchronous operation with retry logic
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicy.ExecuteAsync(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Executes an async operation without return value
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicy.CalculateDelay(System.Int32)">
            <summary>
            Calculates delay for the given attempt using exponential backoff
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicy.DefaultShouldRetry(System.Exception)">
            <summary>
            Default retry condition - retries on most exceptions except critical ones
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicy.GetStatistics">
            <summary>
            Gets retry policy statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryExhaustedException">
            <summary>
            Exception thrown when all retry attempts are exhausted
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicyStatistics">
            <summary>
            Retry policy statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicyBuilder">
            <summary>
            Retry policy builder for fluent configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicies">
            <summary>
            Predefined retry policies for common scenarios
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicies.Fast(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Fast retry policy for quick operations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicies.Standard(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Standard retry policy for normal operations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicies.Slow(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Slow retry policy for expensive operations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RetryPolicies.Network(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Network retry policy for network operations
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.StrategyException">
            <summary>
            Base exception for all strategy-related errors
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.StrategyException.ErrorCode">
            <summary>
            Error code for categorization
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.StrategyException.Severity">
            <summary>
            Error severity level
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.StrategyException.Context">
            <summary>
            Additional error context
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.StrategyException.IsRecoverable">
            <summary>
            Whether this error is recoverable
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.StrategyException.WithContext(System.String,System.Object)">
            <summary>
            Adds context information to the exception
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.SignalGenerationException">
            <summary>
            Exception thrown when signal generation fails
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ValidationException">
            <summary>
            Exception thrown when validation fails
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.MarketDataException">
            <summary>
            Exception thrown when market data is invalid or corrupted
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ConfigurationException">
            <summary>
            Exception thrown when configuration is invalid
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.PerformanceException">
            <summary>
            Exception thrown when performance thresholds are exceeded
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.AtasIntegrationException">
            <summary>
            Exception thrown when ATAS platform integration fails
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.RiskManagementException">
            <summary>
            Exception thrown when risk management limits are exceeded
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.IndicatorException">
            <summary>
            Exception thrown when indicator calculation fails
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.MemoryLimitException">
            <summary>
            Exception thrown when memory limits are exceeded
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.LatencyException">
            <summary>
            Exception thrown when latency thresholds are exceeded
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ErrorSeverity">
            <summary>
            Error severity levels
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ErrorInfo">
            <summary>
            Error information for logging and monitoring
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ExceptionExtensions">
            <summary>
            Extension methods for exception handling
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ExceptionExtensions.ToErrorInfo(System.Exception)">
            <summary>
            Converts an exception to ErrorInfo
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ExceptionExtensions.IsRecoverable(System.Exception)">
            <summary>
            Checks if an exception is recoverable
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.ErrorHandling.ExceptionExtensions.GetSeverity(System.Exception)">
            <summary>
            Gets the error severity of an exception
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1">
            <summary>
            High-performance circular buffer optimized for ATAS market data storage
            </summary>
            <typeparam name="T">Type of items to store</typeparam>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Capacity">
            <summary>
            Buffer capacity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Count">
            <summary>
            Current number of items in buffer
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.IsFull">
            <summary>
            Whether buffer is full
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.IsEmpty">
            <summary>
            Whether buffer is empty
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Utilization">
            <summary>
            Buffer utilization percentage
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Add(`0)">
            <summary>
            Adds an item to the buffer (overwrites oldest if full)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Adds multiple items to the buffer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.GetLatest">
            <summary>
            Gets the most recent item without removing it
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.GetOldest">
            <summary>
            Gets the oldest item without removing it
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.GetAt(System.Int32)">
            <summary>
            Gets item at specified index (0 = oldest, Count-1 = newest)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Item(System.Int32)">
            <summary>
            Indexer for accessing items by index (0 = oldest, Count-1 = newest)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.GetNewest">
            <summary>
            Gets the newest (most recently added) item
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.GetLast(System.Int32)">
            <summary>
            Gets the last N items (most recent first)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.ToArray">
            <summary>
            Gets all items as array (oldest first)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Clear">
            <summary>
            Clears all items from the buffer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Where(System.Func{`0,System.Boolean})">
            <summary>
            Finds items matching the predicate
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Sum">
            <summary>
            Calculates sum of all elements (requires T to be numeric)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.Average">
            <summary>
            Calculates average of all elements
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.CircularBuffer`1.GetStatistics">
            <summary>
            Gets buffer statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.BufferStatistics">
            <summary>
            Buffer statistics for monitoring
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.LockFreeCircularBuffer`1">
            <summary>
            Thread-safe circular buffer for high-frequency scenarios
            </summary>
            <typeparam name="T">Type of items to store</typeparam>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.LockFreeCircularBuffer`1.TryAdd(`0)">
            <summary>
            Attempts to add an item to the buffer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.LockFreeCircularBuffer`1.TryGetLatest(`0@)">
            <summary>
            Attempts to get the latest item
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1">
            <summary>
            High-performance object pool for reducing garbage collection pressure
            </summary>
            <typeparam name="T">Type of objects to pool</typeparam>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1.Count">
            <summary>
            Current number of objects in the pool
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1.MaxSize">
            <summary>
            Maximum pool size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1.Utilization">
            <summary>
            Pool utilization percentage
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1.Get">
            <summary>
            Gets an object from the pool or creates a new one
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1.Return(`0)">
            <summary>
            Returns an object to the pool
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1.Clear">
            <summary>
            Clears all objects from the pool
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool`1.GetStatistics">
            <summary>
            Gets pool statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.PoolStatistics">
            <summary>
            Pool statistics for monitoring
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.PooledObject`1">
            <summary>
            Pooled object wrapper that automatically returns objects to pool
            </summary>
            <typeparam name="T">Type of pooled object</typeparam>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPoolExtensions">
            <summary>
            Extension methods for object pools
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPoolExtensions.GetPooled``1(SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.ObjectPool{``0})">
            <summary>
            Gets a pooled object that will be automatically returned when disposed
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.PoolManager">
            <summary>
            Pool manager for managing multiple object pools
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.PoolManager.GetPool``1(System.Int32,System.Func{``0},System.Action{``0})">
            <summary>
            Gets or creates an object pool for the specified type
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.PoolManager.GetAllStatistics">
            <summary>
            Gets statistics for all pools
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.MemoryManagement.PoolManager.ClearAllPools">
            <summary>
            Clears all pools
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.Performance.IndicatorPerformanceMonitor">
            <summary>
            Performance monitor specifically designed for indicator performance tracking
            Ensures ATAS platform compatibility with less than 20ms processing requirements
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.Performance.IndicatorPerformanceMonitor.RecordProcessingTime(System.String,System.Double,System.Boolean)">
            <summary>
            Records processing time for an indicator
            </summary>
            <param name="indicatorName">Name of the indicator</param>
            <param name="processingTimeMs">Processing time in milliseconds</param>
            <param name="wasSuccessful">Whether the processing was successful</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.Performance.IndicatorPerformanceMonitor.GetIndicatorMetrics(System.String)">
            <summary>
            Gets performance metrics for a specific indicator
            </summary>
            <param name="indicatorName">Name of the indicator</param>
            <returns>Performance metrics or null if not found</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.Performance.IndicatorPerformanceMonitor.GetAllMetrics">
            <summary>
            Gets performance metrics for all indicators
            </summary>
            <returns>Dictionary of indicator metrics</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.Performance.IndicatorPerformanceMonitor.GetPerformanceSummary">
            <summary>
            Gets performance summary for all indicators
            </summary>
            <returns>Performance summary</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.Performance.IndicatorPerformanceMonitor.ResetIndicatorMetrics(System.String)">
            <summary>
            Resets metrics for a specific indicator
            </summary>
            <param name="indicatorName">Name of the indicator</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Infrastructure.Performance.IndicatorPerformanceMonitor.ResetAllMetrics">
            <summary>
            Resets all performance metrics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Infrastructure.Performance.PerformanceSummary">
            <summary>
            Performance summary for all indicators
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker">
            <summary>
            Interface for tracking indicator performance over time
            Provides adaptive selection capabilities based on actual trading results
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.RecordSignalOutcomeAsync(SmartVolumeStrategy.Core.Models.SignalOutcome)">
            <summary>
            Records a signal outcome for performance tracking
            </summary>
            <param name="outcome">Signal outcome data</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.RecordSignalOutcome(SmartVolumeStrategy.Core.Models.SignalOutcome)">
            <summary>
            Records a signal outcome synchronously
            </summary>
            <param name="outcome">Signal outcome data</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.GetIndicatorPerformance(System.String)">
            <summary>
            Gets performance data for a specific indicator
            </summary>
            <param name="indicatorName">Name of the indicator</param>
            <returns>Performance data or null if not found</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.GetAllPerformanceData">
            <summary>
            Gets performance data for all tracked indicators
            </summary>
            <returns>Dictionary of indicator performance data</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.GetPerformanceScore(System.String,SmartVolumeStrategy.Core.Models.TimeWindow,SmartVolumeStrategy.Core.Models.MarketRegime)">
            <summary>
            Gets performance-based score for an indicator (0.0 to 2.0)
            </summary>
            <param name="indicatorName">Name of the indicator</param>
            <param name="timeWindow">Time window for performance calculation</param>
            <param name="currentRegime">Current market regime</param>
            <returns>Performance score multiplier</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.GetTopPerformingIndicators(SmartVolumeStrategy.Core.Models.MarketRegime,SmartVolumeStrategy.Core.Models.TimeWindow,System.Int32)">
            <summary>
            Gets the top performing indicators for a specific regime and time window
            </summary>
            <param name="regime">Market regime</param>
            <param name="timeWindow">Time window</param>
            <param name="count">Number of top indicators to return</param>
            <returns>List of top performing indicator names</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.GetUnderperformingIndicators(System.Double,SmartVolumeStrategy.Core.Models.TimeWindow)">
            <summary>
            Gets indicators that are currently underperforming
            </summary>
            <param name="threshold">Performance threshold below which indicators are considered underperforming</param>
            <param name="timeWindow">Time window for evaluation</param>
            <returns>List of underperforming indicator names</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.UpdatePerformanceMetrics">
            <summary>
            Updates performance windows and calculates trends
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.ResetIndicatorPerformance(System.String)">
            <summary>
            Resets performance data for a specific indicator
            </summary>
            <param name="indicatorName">Name of the indicator to reset</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.ResetAllPerformance">
            <summary>
            Resets all performance data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.GetPerformanceSummary">
            <summary>
            Gets performance summary statistics
            </summary>
            <returns>Performance summary</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.ExportPerformanceDataAsync(System.String,System.String)">
            <summary>
            Exports performance data for analysis
            </summary>
            <param name="filePath">Export file path</param>
            <param name="format">Export format (JSON, CSV)</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.ImportPerformanceDataAsync(System.String)">
            <summary>
            Imports performance data from file
            </summary>
            <param name="filePath">Import file path</param>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.PerformanceUpdated">
            <summary>
            Event fired when performance data is updated
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.TrendChanged">
            <summary>
            Event fired when an indicator's performance trend changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IIndicatorPerformanceTracker.GetTrackerMetrics">
            <summary>
            Gets performance metrics for the tracker itself
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary">
            <summary>
            Performance summary statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.TotalIndicators">
            <summary>
            Total number of indicators tracked
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.TotalSignals">
            <summary>
            Total signals recorded
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.OverallWinRate">
            <summary>
            Overall win rate across all indicators
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.BestPerformingIndicator">
            <summary>
            Best performing indicator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.WorstPerformingIndicator">
            <summary>
            Worst performing indicator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.AveragePerformanceScore">
            <summary>
            Average performance score
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.RegimePerformance">
            <summary>
            Performance by market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.TrendDistribution">
            <summary>
            Performance trends summary
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceSummary.LastUpdated">
            <summary>
            Last update timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.PerformanceUpdatedEventArgs">
            <summary>
            Event arguments for performance updates
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceUpdatedEventArgs.IndicatorName">
            <summary>
            Indicator that was updated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceUpdatedEventArgs.PerformanceData">
            <summary>
            Updated performance data
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceUpdatedEventArgs.SignalOutcome">
            <summary>
            Signal outcome that triggered the update
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceUpdatedEventArgs.Timestamp">
            <summary>
            Update timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.PerformanceTrendChangedEventArgs">
            <summary>
            Event arguments for performance trend changes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrendChangedEventArgs.IndicatorName">
            <summary>
            Indicator whose trend changed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrendChangedEventArgs.PreviousTrend">
            <summary>
            Previous trend
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrendChangedEventArgs.NewTrend">
            <summary>
            New trend
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrendChangedEventArgs.PerformanceData">
            <summary>
            Current performance data
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrendChangedEventArgs.Timestamp">
            <summary>
            Change timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics">
            <summary>
            Performance tracker metrics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics.TotalOutcomesRecorded">
            <summary>
            Total number of outcomes recorded
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics.AverageProcessingTimeMs">
            <summary>
            Average processing time for recording outcomes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics.MemoryUsageMB">
            <summary>
            Memory usage in MB
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics.PerformanceUpdates">
            <summary>
            Number of performance updates performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics.TrendChanges">
            <summary>
            Number of trend changes detected
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics.Uptime">
            <summary>
            Tracker uptime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.PerformanceTrackerMetrics.LastReset">
            <summary>
            Last reset timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider">
            <summary>
            Interface for market data abstraction with ATAS platform integration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.GetRealTimeDataAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets real-time market data stream
            </summary>
            <param name="symbol">Trading symbol</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Async enumerable of market data</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.GetHistoricalDataAsync(System.String,System.DateTime,System.DateTime,SmartVolumeStrategy.Core.Models.TimeFrame,System.Threading.CancellationToken)">
            <summary>
            Gets historical market data for backtesting and analysis
            </summary>
            <param name="symbol">Trading symbol</param>
            <param name="startTime">Start time for historical data</param>
            <param name="endTime">End time for historical data</param>
            <param name="timeframe">Data timeframe</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Historical market data</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.GetMarketDepthAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets current market depth (order book) data
            </summary>
            <param name="symbol">Trading symbol</param>
            <param name="levels">Number of depth levels to retrieve</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Market depth data</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.GetMarketStatisticsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets current market statistics and session information
            </summary>
            <param name="symbol">Trading symbol</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Market statistics</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.SubscribeToTicksAsync(System.String,System.Action{SmartVolumeStrategy.Core.Models.TickData},System.Threading.CancellationToken)">
            <summary>
            Subscribes to real-time tick data
            </summary>
            <param name="symbol">Trading symbol</param>
            <param name="onTick">Callback for each tick</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Subscription handle</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.SubscribeToDepthAsync(System.String,System.Action{SmartVolumeStrategy.Core.Models.MarketDepth},System.Threading.CancellationToken)">
            <summary>
            Subscribes to order book changes
            </summary>
            <param name="symbol">Trading symbol</param>
            <param name="onDepthChange">Callback for depth changes</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Subscription handle</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.GetDataQuality">
            <summary>
            Gets data quality metrics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.ConnectionStatus">
            <summary>
            Current connection status
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.ConnectionStatusChanged">
            <summary>
            Event fired when connection status changes
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.IMarketDataProvider.DataQualityIssueDetected">
            <summary>
            Event fired when data quality issues are detected
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IDataSubscription">
            <summary>
            Interface for data subscription management
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IDataSubscription.Id">
            <summary>
            Unique subscription identifier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IDataSubscription.Symbol">
            <summary>
            Symbol being subscribed to
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IDataSubscription.IsActive">
            <summary>
            Whether subscription is currently active
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IDataSubscription.PauseAsync">
            <summary>
            Pauses the subscription
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IDataSubscription.ResumeAsync">
            <summary>
            Resumes the subscription
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IDataSubscription.GetStats">
            <summary>
            Gets subscription statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.DataQualityMetrics">
            <summary>
            Data quality metrics for monitoring feed health
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.ConnectionStatus">
            <summary>
            Connection status enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.ConnectionStatusChangedEventArgs">
            <summary>
            Event args for connection status changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ConnectionStatusChangedEventArgs.#ctor(SmartVolumeStrategy.Core.Interfaces.ConnectionStatus,SmartVolumeStrategy.Core.Interfaces.ConnectionStatus,System.String)">
            <summary>
            Event args for connection status changes
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.DataQualityIssue">
            <summary>
            Data quality issue notification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.DataQualityIssue.#ctor(System.String,System.String,System.String,System.DateTime,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Data quality issue notification
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.SubscriptionStats">
            <summary>
            Subscription statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor">
            <summary>
            Interface for comprehensive performance monitoring and metrics collection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.RecordMetric(System.String,System.Double,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Records a performance metric with timestamp
            </summary>
            <param name="metricName">Name of the metric</param>
            <param name="value">Metric value</param>
            <param name="tags">Optional tags for categorization</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.RecordExecutionTime(System.String,System.Double,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Records execution time for an operation
            </summary>
            <param name="operationName">Name of the operation</param>
            <param name="executionTimeMs">Execution time in milliseconds</param>
            <param name="tags">Optional tags for categorization</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.IncrementCounter(System.String,System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Records a counter increment
            </summary>
            <param name="counterName">Name of the counter</param>
            <param name="increment">Increment value (default: 1)</param>
            <param name="tags">Optional tags for categorization</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.RecordTrade(SmartVolumeStrategy.Core.Models.TradeResult)">
            <summary>
            Records trading performance metrics
            </summary>
            <param name="trade">Completed trade information</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.GetMetricsAsync(System.String,SmartVolumeStrategy.Core.Models.TimeRange)">
            <summary>
            Gets current performance metrics
            </summary>
            <param name="metricName">Specific metric name (optional)</param>
            <param name="timeRange">Time range for metrics (optional)</param>
            <returns>Performance metrics</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.GetSystemStats">
            <summary>
            Gets real-time system performance statistics
            </summary>
            <returns>System performance stats</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.GetTradingPerformanceAsync(SmartVolumeStrategy.Core.Models.TimeRange)">
            <summary>
            Gets trading performance summary
            </summary>
            <param name="timeRange">Time range for analysis</param>
            <returns>Trading performance summary</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.StartOperation(System.String)">
            <summary>
            Starts monitoring a long-running operation
            </summary>
            <param name="operationName">Name of the operation</param>
            <returns>Operation monitor handle</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.ExportDataAsync(System.String,SmartVolumeStrategy.Core.Interfaces.ExportFormat,SmartVolumeStrategy.Core.Models.TimeRange,System.Threading.CancellationToken)">
            <summary>
            Exports performance data to file
            </summary>
            <param name="filePath">Export file path</param>
            <param name="format">Export format</param>
            <param name="timeRange">Time range to export</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.SetAlertThresholds(System.Collections.Generic.IEnumerable{SmartVolumeStrategy.Core.Interfaces.PerformanceAlert})">
            <summary>
            Sets performance alert thresholds
            </summary>
            <param name="alerts">Alert configurations</param>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.AlertTriggered">
            <summary>
            Event fired when performance alert is triggered
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.IPerformanceMonitor.MetricsUpdated">
            <summary>
            Event fired when metrics are updated
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IOperationMonitor">
            <summary>
            Interface for monitoring individual operations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IOperationMonitor.OperationName">
            <summary>
            Operation name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IOperationMonitor.StartTime">
            <summary>
            Start time of the operation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IOperationMonitor.RecordCheckpoint(System.String)">
            <summary>
            Records a checkpoint in the operation
            </summary>
            <param name="checkpointName">Name of the checkpoint</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IOperationMonitor.AddMetadata(System.String,System.Object)">
            <summary>
            Adds metadata to the operation
            </summary>
            <param name="key">Metadata key</param>
            <param name="value">Metadata value</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IOperationMonitor.Complete">
            <summary>
            Marks the operation as completed successfully
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IOperationMonitor.Fail(System.Exception)">
            <summary>
            Marks the operation as failed
            </summary>
            <param name="error">Error information</param>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.SystemPerformanceStats">
            <summary>
            System performance statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.TradingPerformanceSummary">
            <summary>
            Trading performance summary
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.PerformanceAlert">
            <summary>
            Performance alert configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.AlertCondition">
            <summary>
            Alert condition types
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.ExportFormat">
            <summary>
            Export format options
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.PerformanceAlertTriggered">
            <summary>
            Performance alert triggered event
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.PerformanceAlertTriggered.#ctor(SmartVolumeStrategy.Core.Interfaces.PerformanceAlert,System.Double,System.DateTime)">
            <summary>
            Performance alert triggered event
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.MetricsUpdated">
            <summary>
            Metrics updated event
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.MetricsUpdated.#ctor(System.String,System.Double,System.DateTime,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Metrics updated event
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator">
            <summary>
            Interface for generating trading signals with async support and high-probability filtering
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.GenerateSignalAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Generates a trading signal based on current market data
            </summary>
            <param name="marketData">Current market data</param>
            <param name="cancellationToken">Cancellation token for async operations</param>
            <returns>Generated trading signal with confidence score</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.GenerateSignalBatchAsync(System.Collections.Generic.IEnumerable{SmartVolumeStrategy.Core.Models.MarketData},System.Threading.CancellationToken)">
            <summary>
            Generates multiple signals for batch processing
            </summary>
            <param name="marketDataBatch">Batch of market data</param>
            <param name="cancellationToken">Cancellation token for async operations</param>
            <returns>Collection of generated signals</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.UpdateMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates signal generator with new market data for real-time processing
            </summary>
            <param name="marketData">Latest market data</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.ConfidenceThreshold">
            <summary>
            Gets the current confidence threshold for signal generation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.SetConfidenceThreshold(System.Double)">
            <summary>
            Sets the confidence threshold for signal filtering
            </summary>
            <param name="threshold">New confidence threshold (0.0 to 1.0)</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.SetIndicatorAlignmentThreshold(System.Int32)">
            <summary>
            Sets the minimum indicator alignment requirement for signal generation
            </summary>
            <param name="threshold">Minimum number of indicators that must align (2-9)</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.GetStatistics">
            <summary>
            Gets the current signal generation statistics
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.HighConfidenceSignalGenerated">
            <summary>
            Event fired when a high-confidence signal is generated
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.ISignalGenerator.StatisticsUpdated">
            <summary>
            Event fired when signal generation performance metrics are updated
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.SignalGenerationStats">
            <summary>
            Statistics for signal generation performance monitoring
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine">
            <summary>
            Interface for Smart Indicator Selection Engine
            Provides intelligent indicator selection based on market conditions and user preferences
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine.SelectIndicatorsAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Collections.Generic.List{SmartVolumeStrategy.Core.Indicators.IndicatorReading},System.Int32,SmartVolumeStrategy.Core.Models.UserIndicatorPreferences)">
            <summary>
            Selects optimal indicators based on market conditions and user preferences
            </summary>
            <param name="marketData">Current market data</param>
            <param name="availableIndicators">List of available indicator readings</param>
            <param name="requiredCount">Number of indicators to select</param>
            <param name="preferences">User preferences for indicator selection</param>
            <returns>Selection result with chosen indicators and reasoning</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine.SelectIndicators(SmartVolumeStrategy.Core.Models.MarketData,System.Collections.Generic.List{SmartVolumeStrategy.Core.Indicators.IndicatorReading},System.Int32,SmartVolumeStrategy.Core.Models.UserIndicatorPreferences)">
            <summary>
            Selects optimal indicators synchronously (for performance-critical paths)
            </summary>
            <param name="marketData">Current market data</param>
            <param name="availableIndicators">List of available indicator readings</param>
            <param name="requiredCount">Number of indicators to select</param>
            <param name="preferences">User preferences for indicator selection</param>
            <returns>Selection result with chosen indicators and reasoning</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine.UpdateMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates the selection engine with new market data for regime detection
            </summary>
            <param name="marketData">Latest market data</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine.CurrentMarketRegime">
            <summary>
            Gets the current market regime as detected by the engine
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine.LastSelectionResult">
            <summary>
            Gets the last selection result for transparency
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine.SelectionChanged">
            <summary>
            Event fired when indicator selection changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.ISmartIndicatorSelectionEngine.GetPerformanceMetrics">
            <summary>
            Gets performance metrics for the selection engine
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IndicatorSelectionChangedEventArgs">
            <summary>
            Event arguments for selection change events
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IValidationEngine">
            <summary>
            Interface for validation engines
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IValidationEngine.ValidateSignalAsync(SmartVolumeStrategy.Core.Models.TradingSignal,System.Threading.CancellationToken)">
            <summary>
            Validates a trading signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IValidationEngine.ValidateMarketDataAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Validates market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IValidationEngine.GetStatistics">
            <summary>
            Gets validation engine statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.IValidationRule">
            <summary>
            Interface for validation rules
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IValidationRule.Priority">
            <summary>
            Rule priority (1 = highest, 5 = lowest)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.IValidationRule.Name">
            <summary>
            Rule name for identification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Interfaces.IValidationRule.ValidateAsync(SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates the given context
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.ValidationContext">
            <summary>
            Validation context for rules
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.ValidationContext.Signal">
            <summary>
            Trading signal being validated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.ValidationContext.Timestamp">
            <summary>
            Validation timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.ValidationContext.ValidationId">
            <summary>
            Unique validation identifier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Interfaces.ValidationContext.Metadata">
            <summary>
            Additional metadata for validation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Interfaces.ValidationEngineStats">
            <summary>
            Validation engine statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.LearningTradeResult">
            <summary>
            Extended trade result with learning-specific data
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.ConfidenceScore">
            <summary>
            Confidence score when signal was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.IndicatorsAligned">
            <summary>
            Number of indicators aligned when signal was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.MarketRegime">
            <summary>
            Market regime at time of trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.IndicatorValues">
            <summary>
            Indicator values at time of signal generation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.MarketConditions">
            <summary>
            Market conditions at time of trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.DurationSeconds">
            <summary>
            Trade duration in seconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.AchievedTarget">
            <summary>
            Whether this trade achieved the target profit (0.5%)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningTradeResult.RiskAdjustedReturn">
            <summary>
            Risk-adjusted return
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.MarketConditions">
            <summary>
            Market conditions at time of trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MarketConditions.Volatility">
            <summary>
            Volatility level (0-1 scale)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MarketConditions.VolumeActivity">
            <summary>
            Volume activity level (0-1 scale)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MarketConditions.TrendStrength">
            <summary>
            Trend strength (-1 to 1, negative=down, positive=up)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MarketConditions.OrderBookImbalance">
            <summary>
            Order book imbalance (-1 to 1, negative=sell pressure, positive=buy pressure)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MarketConditions.TimeOfDayFactor">
            <summary>
            Time of day factor (0-1, based on market session)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MarketConditions.SpreadTightness">
            <summary>
            Spread tightness (0-1, 1=very tight spread)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer">
            <summary>
            Performance data container for JSON persistence
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.Trades">
            <summary>
            All trade results
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.DailyStats">
            <summary>
            Daily performance aggregations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.WeeklyStats">
            <summary>
            Weekly performance aggregations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.MonthlyStats">
            <summary>
            Monthly performance aggregations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.LearningInsights">
            <summary>
            Learning insights generated from analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.OptimizationHistory">
            <summary>
            Parameter optimization history
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.LastUpdated">
            <summary>
            Last update timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.Version">
            <summary>
            Data format version for migration support
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataContainer.UpdateDailyStats(SmartVolumeStrategy.Core.Learning.LearningTradeResult)">
            <summary>
            Update daily statistics with new trade
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats">
            <summary>
            Daily performance statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.Date">
            <summary>
            Date for these statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.TotalTrades">
            <summary>
            Total trades for the day
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.WinningTrades">
            <summary>
            Winning trades count
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.TotalPnL">
            <summary>
            Total profit/loss for the day
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.AverageConfidence">
            <summary>
            Average confidence score
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.DominantRegime">
            <summary>
            Dominant market regime for the day
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.WinRate">
            <summary>
            Win rate for the day
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.DailyPerformanceStats.AddTrade(SmartVolumeStrategy.Core.Learning.LearningTradeResult)">
            <summary>
            Add a trade to daily statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats">
            <summary>
            Weekly performance statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats.WeekStart">
            <summary>
            Week start date
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats.TotalTrades">
            <summary>
            Total trades for the week
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats.WinningTrades">
            <summary>
            Winning trades count
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats.TotalPnL">
            <summary>
            Total profit/loss for the week
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats.BestDay">
            <summary>
            Best performing day
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats.WorstDay">
            <summary>
            Worst performing day
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.WeeklyPerformanceStats.WinRate">
            <summary>
            Win rate for the week
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats">
            <summary>
            Monthly performance statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats.Month">
            <summary>
            Month and year
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats.TotalTrades">
            <summary>
            Total trades for the month
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats.WinningTrades">
            <summary>
            Winning trades count
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats.TotalPnL">
            <summary>
            Total profit/loss for the month
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats.MaxDrawdown">
            <summary>
            Maximum drawdown during the month
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats.SharpeRatio">
            <summary>
            Sharpe ratio for the month
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.MonthlyPerformanceStats.WinRate">
            <summary>
            Win rate for the month
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.LearningInsight">
            <summary>
            Learning insight generated from pattern analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.Id">
            <summary>
            Unique identifier for the insight
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.Timestamp">
            <summary>
            Timestamp when insight was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.Type">
            <summary>
            Type of insight
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.Description">
            <summary>
            Insight description
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.Confidence">
            <summary>
            Confidence level of the insight (0-1)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.ParameterRecommendations">
            <summary>
            Recommended parameter adjustments
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.SupportingData">
            <summary>
            Supporting data for the insight
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.Applied">
            <summary>
            Whether this insight has been applied
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningInsight.PerformanceImpact">
            <summary>
            Performance impact after applying the insight
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.LearningInsightType">
            <summary>
            Types of learning insights
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult">
            <summary>
            Parameter optimization result
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.Timestamp">
            <summary>
            Optimization timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.ParameterName">
            <summary>
            Parameter name that was optimized
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.OriginalValue">
            <summary>
            Original parameter value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.OptimizedValue">
            <summary>
            Optimized parameter value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.PerformanceImprovement">
            <summary>
            Performance improvement achieved
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.OptimizationMethod">
            <summary>
            Optimization method used
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.Iterations">
            <summary>
            Number of iterations required
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.ParameterOptimizationResult.StatisticalSignificance">
            <summary>
            Statistical significance of the improvement
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator">
            <summary>
            Coordinates all learning system components and provides unified interface
            Integrates with existing adaptive system for seamless operation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.LearningIntervalMinutes">
            <summary>
            Learning analysis interval in minutes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.MinimumTradesForLearning">
            <summary>
            Minimum trades required before triggering learning analysis
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.LearningInsightsGenerated">
            <summary>
            Event fired when new learning insights are generated
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.ParameterOptimizationCompleted">
            <summary>
            Event fired when parameter optimization is completed
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.RecordTradeAsync(SmartVolumeStrategy.Core.Learning.LearningTradeResult,System.Threading.CancellationToken)">
            <summary>
            Records a trade result and triggers learning analysis if conditions are met
            </summary>
            <param name="trade">Trade result to record</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.PerformLearningAnalysisAsync(System.Threading.CancellationToken)">
            <summary>
            Performs comprehensive learning analysis and generates insights
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Generated learning insights</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.OptimizeParametersAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Analytics.ParameterDefinition},System.Threading.CancellationToken)">
            <summary>
            Performs parameter optimization using historical data
            </summary>
            <param name="parametersToOptimize">Parameters to optimize</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Optimization results</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.GetMLPredictionsAsync(SmartVolumeStrategy.Core.Learning.MarketFeatures,System.Collections.Generic.Dictionary{System.String,System.Double},System.Threading.CancellationToken)">
            <summary>
            Gets ML-based predictions for current market conditions
            </summary>
            <param name="marketFeatures">Current market features</param>
            <param name="indicatorValues">Current indicator values</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>ML predictions</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.GetRecentPerformanceAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Gets recent performance statistics
            </summary>
            <param name="timeWindow">Time window for statistics</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Performance statistics</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.SetLearningEnabled(System.Boolean)">
            <summary>
            Enables or disables the learning system
            </summary>
            <param name="enabled">Whether learning should be enabled</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.GetSystemStatus">
            <summary>
            Gets the current status of the learning system
            </summary>
            <returns>Learning system status</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.ForceLearningAnalysisAsync(System.Threading.CancellationToken)">
            <summary>
            Forces immediate learning analysis regardless of schedule
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Learning analysis result</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.LearningSystemCoordinator.CreateDataBackupAsync(System.Threading.CancellationToken)">
            <summary>
            Creates a backup of all learning data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.MLNetIntegration">
            <summary>
            ML.NET integration for lightweight machine learning models
            Provides threshold optimization and market regime classification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.PredictOptimalThresholdAsync(SmartVolumeStrategy.Core.Learning.MarketFeatures,System.Threading.CancellationToken)">
            <summary>
            Predicts optimal confidence threshold based on current market features
            </summary>
            <param name="features">Current market features</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Predicted optimal threshold</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.PredictMarketRegimeAsync(SmartVolumeStrategy.Core.Learning.MarketFeatures,System.Threading.CancellationToken)">
            <summary>
            Predicts market regime based on current market features
            </summary>
            <param name="features">Current market features</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Predicted market regime</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.PredictSignalQualityAsync(SmartVolumeStrategy.Core.Learning.MarketFeatures,System.Collections.Generic.Dictionary{System.String,System.Double},System.Threading.CancellationToken)">
            <summary>
            Predicts signal quality score based on market features and indicator values
            </summary>
            <param name="features">Market features</param>
            <param name="indicatorValues">Current indicator values</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Predicted signal quality (0-1)</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.TrainModelsAsync(System.Threading.CancellationToken)">
            <summary>
            Trains all ML models using historical performance data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.EnsureModelsTrainedAsync(System.Threading.CancellationToken)">
            <summary>
            Ensures models are trained and up-to-date
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.TrainThresholdModelAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Trains the threshold optimization model using regression
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.TrainRegimeModelAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Trains the market regime classification model
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.TrainSignalQualityModelAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Trains the signal quality prediction model
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.CalculateOptimalThreshold(SmartVolumeStrategy.Core.Learning.LearningTradeResult)">
            <summary>
            Calculates optimal threshold based on trade outcome
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.CreateSignalFeatures(SmartVolumeStrategy.Core.Learning.MarketFeatures,System.Collections.Generic.Dictionary{System.String,System.Double})">
            <summary>
            Creates signal features from market features and indicator values
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.CreateSignalTrainingData(SmartVolumeStrategy.Core.Learning.LearningTradeResult)">
            <summary>
            Creates signal training data from trade result
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.CalculateSignalQuality(SmartVolumeStrategy.Core.Learning.LearningTradeResult)">
            <summary>
            Calculates signal quality based on trade outcome
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.GetFallbackThreshold(SmartVolumeStrategy.Core.Learning.MarketFeatures)">
            <summary>
            Fallback threshold calculation when ML model is unavailable
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.GetFallbackRegime(SmartVolumeStrategy.Core.Learning.MarketFeatures)">
            <summary>
            Fallback regime classification when ML model is unavailable
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.MLNetIntegration.GetFallbackSignalQuality(SmartVolumeStrategy.Core.Learning.MarketFeatures,System.Collections.Generic.Dictionary{System.String,System.Double})">
            <summary>
            Fallback signal quality calculation when ML model is unavailable
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.PatternLearningEngine">
            <summary>
            Analyzes historical performance data to identify success and failure patterns
            Implements pseudo-machine learning algorithms for parameter optimization
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.MinimumTradesForAnalysis">
            <summary>
            Minimum number of trades required for reliable pattern analysis
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.TargetProfitPercentage">
            <summary>
            Target profit percentage for success classification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.AnalyzeSuccessPatternsAsync(System.Threading.CancellationToken)">
            <summary>
            Analyzes success patterns from historical trades
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Learning insights with pattern analysis</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.AnalyzeOptimalConfidenceRangeAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes optimal confidence score ranges for successful trades
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.AnalyzeOptimalIndicatorCountAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes optimal number of aligned indicators for successful trades
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.AnalyzeBestTimeWindowsAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes best time windows for trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.AnalyzeWorstConditionsAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes worst market conditions that lead to failures
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.AnalyzeRegimePerformanceAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Analyzes performance by market regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.GenerateParameterRecommendationsAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult})">
            <summary>
            Generates parameter optimization recommendations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.FindOptimalRange(System.Collections.Generic.List{System.Double})">
            <summary>
            Finds optimal range using percentile analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.CalculateSuccessRateInRange(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningTradeResult},System.Double,System.Double)">
            <summary>
            Calculates success rate within a specific range
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PatternLearningEngine.CalculateInsightConfidence(System.Int32,System.Int32)">
            <summary>
            Calculates confidence level for an insight based on sample size and success rate
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.StatisticalExtensions">
            <summary>
            Extension methods for statistical calculations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.StatisticalExtensions.StandardDeviation(System.Collections.Generic.IEnumerable{System.Double})">
            <summary>
            Calculates standard deviation of a sequence
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.PerformanceDataManager">
            <summary>
            Manages persistence and retrieval of performance data for learning system
            ATAS-compatible file operations using DataPath
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.#ctor(System.String,Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.Core.Learning.PerformanceDataManager})">
            <summary>
            Initializes the performance data manager
            </summary>
            <param name="dataPath">ATAS DataPath for file operations</param>
            <param name="logger">Logger instance</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.SaveTradeResultAsync(SmartVolumeStrategy.Core.Learning.LearningTradeResult,System.Threading.CancellationToken)">
            <summary>
            Saves a trade result and triggers learning analysis if threshold is met
            </summary>
            <param name="trade">Trade result to save</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.LoadPerformanceDataAsync(System.Threading.CancellationToken)">
            <summary>
            Loads all performance data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Performance data container</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.GetRecentTradesAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Gets recent trades within specified time window
            </summary>
            <param name="timeWindow">Time window to look back</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Recent trades</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.GetTradesByRegimeAsync(SmartVolumeStrategy.Core.Models.MarketRegime,System.Threading.CancellationToken)">
            <summary>
            Gets trades filtered by market regime
            </summary>
            <param name="regime">Market regime to filter by</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Trades for the specified regime</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.SaveLearningInsightsAsync(System.Collections.Generic.List{SmartVolumeStrategy.Core.Learning.LearningInsight},System.Threading.CancellationToken)">
            <summary>
            Saves learning insights
            </summary>
            <param name="insights">Learning insights to save</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            Forces immediate save of cached data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.CreateBackupAsync(System.Threading.CancellationToken)">
            <summary>
            Creates a backup of current performance data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Learning.PerformanceDataManager.GetPerformanceStatisticsAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets performance statistics for a date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Performance statistics</returns>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Learning.PerformanceStatistics">
            <summary>
            Performance statistics summary
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdManager">
            <summary>
            Adaptive threshold management system
            Dynamically adjusts strategy parameters based on market regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdManager.GetThresholds(SmartVolumeStrategy.Core.Regime.MarketRegime)">
            <summary>
            Gets adaptive thresholds for the specified market regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdManager.GetAdaptiveThresholds(SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis)">
            <summary>
            Gets adaptive thresholds with regime analysis context
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdManager.UpdateRegimeConfiguration(SmartVolumeStrategy.Core.Regime.MarketRegime,SmartVolumeStrategy.Core.Regime.RegimeConfiguration)">
            <summary>
            Updates configuration for a specific regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdManager.GetStatistics">
            <summary>
            Gets performance statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdResult">
            <summary>
            Result of adaptive threshold calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdResult.Configuration">
            <summary>
            Final adjusted configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdResult.BaseConfiguration">
            <summary>
            Original base configuration before adjustments
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdResult.RegimeAnalysis">
            <summary>
            Market regime analysis that drove the adjustments
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdResult.AdjustmentReason">
            <summary>
            Reason for the adjustments made
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdResult.Timestamp">
            <summary>
            When this threshold calculation was performed
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdStatistics">
            <summary>
            Performance statistics for adaptive threshold management
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdStatistics.TotalAdjustments">
            <summary>
            Total number of threshold adjustments made
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdStatistics.LastAdjustment">
            <summary>
            When the last adjustment was made
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdStatistics.ConfiguredRegimes">
            <summary>
            Number of configured regimes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdStatistics.AverageConfidenceThreshold">
            <summary>
            Average confidence threshold across all regimes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.AdaptiveThresholdStatistics.AverageIndicatorAlignment">
            <summary>
            Average indicator alignment requirement across all regimes
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.MarketRegimeDetector">
            <summary>
            Advanced market regime detection system
            Analyzes market conditions to determine optimal strategy behavior
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Regime.MarketRegimeDetector.AnalyzeMarketRegime(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Analyzes current market conditions and determines regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Regime.MarketRegimeDetector.GetRegimeStabilityTrend">
            <summary>
            Gets the current regime stability trend
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Regime.MarketRegimeDetector.ValidateRegimeAcrossTimeframes(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Validates regime across multiple timeframes
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.MarketRegime">
            <summary>
            Market regime classification for adaptive strategy behavior
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Regime.MarketRegime.Momentum">
            <summary>
            Strong directional moves with high volume and velocity
            Characteristics: Trend strength greater than 0.7, Volume greater than 1.5x average, Low noise
            Strategy: Aggressive thresholds, fast execution, single indicator sufficient
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Regime.MarketRegime.Ranging">
            <summary>
            Sideways movement with low trend strength
            Characteristics: Trend strength less than 0.3, Normal volume, High noise
            Strategy: Conservative thresholds, multiple confirmations required
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Regime.MarketRegime.Volatile">
            <summary>
            High volatility with mixed signals and rapid changes
            Characteristics: High volatility greater than 2x average, Mixed directions
            Strategy: Medium thresholds, adaptive to rapid changes
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Regime.MarketRegime.Quiet">
            <summary>
            Low activity periods with minimal movement
            Characteristics: Low volume less than 0.7x average, Low volatility
            Strategy: High thresholds, patient approach, quality over quantity
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Regime.MarketRegime.Transition">
            <summary>
            Transitioning between regimes - unstable period
            Characteristics: Conflicting signals, changing patterns
            Strategy: Cautious approach, wait for regime stabilization
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis">
            <summary>
            Comprehensive market regime analysis result
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.PrimaryRegime">
            <summary>
            Primary detected market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.SecondaryRegime">
            <summary>
            Secondary regime (for mixed conditions)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.RegimeStrength">
            <summary>
            Strength of the regime classification (0.0 to 1.0)
            Higher values indicate more confident regime detection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.RegimeStability">
            <summary>
            Stability of the current regime (0.0 to 1.0)
            Higher values indicate the regime is likely to persist
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.TransitionProbability">
            <summary>
            Probability of regime transition in next period (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.VolatilityProfile">
            <summary>
            Detailed volatility profile
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.TrendProfile">
            <summary>
            Detailed trend profile
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.VolumeProfile">
            <summary>
            Detailed volume profile
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.Timestamp">
            <summary>
            When this analysis was performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis.Metadata">
            <summary>
            Additional metadata for debugging and analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.VolatilityProfile">
            <summary>
            Volatility characteristics of the current market
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolatilityProfile.RelativeVolatility">
            <summary>
            Current volatility level relative to historical average
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolatilityProfile.VolatilityTrend">
            <summary>
            Volatility trend (increasing, decreasing, stable)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolatilityProfile.ClusteringFactor">
            <summary>
            Volatility clustering factor (tendency for high/low vol to persist)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolatilityProfile.IntradayPattern">
            <summary>
            Intraday volatility pattern
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.TrendProfile">
            <summary>
            Trend characteristics of the current market
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.TrendProfile.TrendStrength">
            <summary>
            Overall trend strength (0.0 = no trend, 1.0 = very strong trend)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.TrendProfile.TrendDirection">
            <summary>
            Trend direction (-1.0 = strong down, 0.0 = no trend, 1.0 = strong up)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.TrendProfile.TrendConsistency">
            <summary>
            Trend consistency (how consistent the trend has been)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.TrendProfile.TrendAcceleration">
            <summary>
            Trend acceleration (is the trend accelerating or decelerating)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.TrendProfile.MultiTimeframeAlignment">
            <summary>
            Multiple timeframe trend alignment
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.VolumeProfile">
            <summary>
            Volume characteristics of the current market
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolumeProfile.RelativeVolume">
            <summary>
            Current volume relative to average
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolumeProfile.VolumeTrend">
            <summary>
            Volume trend (increasing, decreasing, stable)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolumeProfile.VolumePriceCorrelation">
            <summary>
            Volume-price relationship strength
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolumeProfile.VolumeDistribution">
            <summary>
            Volume distribution (concentrated vs distributed)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.VolumeProfile.InstitutionalVolumeSignal">
            <summary>
            Institutional volume indicators
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.RegimeConfiguration">
            <summary>
            Regime-specific configuration parameters
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.Regime">
            <summary>
            Target market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.ConfidenceThreshold">
            <summary>
            Confidence threshold for this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.IndicatorAlignment">
            <summary>
            Required indicator alignment count
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.VolumeThreshold">
            <summary>
            Minimum volume threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.SignalIntervalSeconds">
            <summary>
            Minimum signal interval in seconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.MaxSignalsPerHour">
            <summary>
            Maximum signals per hour for this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.RiskMultiplier">
            <summary>
            Risk multiplier for position sizing
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.UrgencyMultiplier">
            <summary>
            Signal urgency multiplier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeConfiguration.AllowEmergencyBypass">
            <summary>
            Whether emergency bypasses are allowed in this regime
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Regime.RegimeValidationResult">
            <summary>
            Multi-timeframe regime validation result
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeValidationResult.ConsensusRegime">
            <summary>
            Consensus regime across timeframes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeValidationResult.ConsensusStrength">
            <summary>
            Strength of consensus (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeValidationResult.IsStrongConsensus">
            <summary>
            Whether there's strong consensus (greater than or equal to 67% agreement)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeValidationResult.ConflictingRegimes">
            <summary>
            Conflicting regimes detected in other timeframes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Regime.RegimeValidationResult.TimeframeBreakdown">
            <summary>
            Timeframe-specific regime breakdown
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MarketRegime">
            <summary>
            Market regime classification for adaptive parameter management
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.Unknown">
            <summary>
            Unknown or insufficient data for classification
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.Trending">
            <summary>
            Strong directional movement with normal volatility
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.Ranging">
            <summary>
            Sideways movement with normal volatility
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.VolatileTrending">
            <summary>
            High volatility with trending characteristics
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.VolatileRanging">
            <summary>
            High volatility with ranging characteristics
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.CalmTrending">
            <summary>
            Low volatility with trending characteristics
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.CalmRanging">
            <summary>
            Low volatility with ranging characteristics
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.Breakout">
            <summary>
            Breakout conditions with volume surge
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.MarketRegime.Consolidation">
            <summary>
            Consolidation phase with mixed signals
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.ParameterAdjustment">
            <summary>
            Adaptive parameter adjustment record
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.Timestamp">
            <summary>
            Timestamp of the adjustment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.Component">
            <summary>
            Indicator or component being adjusted
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.ParameterName">
            <summary>
            Parameter name being adjusted
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.OldValue">
            <summary>
            Previous parameter value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.NewValue">
            <summary>
            New parameter value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.Reason">
            <summary>
            Reason for the adjustment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.TriggeringRegime">
            <summary>
            Market regime that triggered the adjustment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ParameterAdjustment.PerformanceMetrics">
            <summary>
            Performance metrics that influenced the adjustment
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics">
            <summary>
            Performance tracking for adaptive system
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.WindowStart">
            <summary>
            Rolling window start time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.WindowEnd">
            <summary>
            Rolling window end time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.TotalSignals">
            <summary>
            Total number of signals generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.WinningTrades">
            <summary>
            Number of winning trades
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.LosingTrades">
            <summary>
            Number of losing trades
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.WinRate">
            <summary>
            Win rate percentage (0-1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.AverageWin">
            <summary>
            Average profit per winning trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.AverageLoss">
            <summary>
            Average loss per losing trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.ProfitFactor">
            <summary>
            Profit factor (gross profit / gross loss)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.SignalFrequency">
            <summary>
            Signal frequency (signals per hour)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.AverageConfidence">
            <summary>
            Average confidence of generated signals
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptivePerformanceMetrics.DominantRegime">
            <summary>
            Current market regime during this period
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdaptiveSettings">
            <summary>
            Adaptive system configuration settings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSettings.AggressivenessLevel">
            <summary>
            Aggressiveness level (1=Conservative, 3=Balanced, 5=Aggressive)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSettings.EnableAutoAdaptation">
            <summary>
            Enable automatic parameter adaptation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSettings.LearningSensitivity">
            <summary>
            Learning sensitivity (0.1=Slow Learning, 1.0=Fast Learning)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSettings.MinWinRateThreshold">
            <summary>
            Minimum win rate threshold for adaptation triggers
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSettings.PerformanceWindowHours">
            <summary>
            Performance monitoring window in hours
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSettings.MaxParameterAdjustment">
            <summary>
            Maximum parameter adjustment per adaptation cycle
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSettings.MinAdaptationIntervalMinutes">
            <summary>
            Minimum time between adaptations in minutes
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus">
            <summary>
            Current status of the adaptive system
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.CurrentRegime">
            <summary>
            Current market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.CurrentWinRate">
            <summary>
            Current win rate in monitoring window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.LastAdaptation">
            <summary>
            Time of last adaptation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.CurrentThresholds">
            <summary>
            Current parameter thresholds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.AdaptationCount">
            <summary>
            Number of adaptations in current session
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.IsAdaptationEnabled">
            <summary>
            Whether auto-adaptation is currently enabled
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.VolatilityScore">
            <summary>
            Current volatility score
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.TrendScore">
            <summary>
            Current trend strength score
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.VolumeScore">
            <summary>
            Current volume activity score
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus.RecentPerformance">
            <summary>
            Recent performance metrics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.RegimeParameterSet">
            <summary>
            Regime-specific parameter configuration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.Regime">
            <summary>
            Target market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.ConfidenceThresholdAdjustment">
            <summary>
            Confidence threshold adjustment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.IndicatorAlignmentAdjustment">
            <summary>
            Minimum indicator alignment adjustment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.VolumeThresholdMultiplier">
            <summary>
            Volume threshold multiplier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.VWAPDeviationAdjustment">
            <summary>
            VWAP deviation threshold adjustment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.RSIThresholdAdjustments">
            <summary>
            RSI threshold adjustments
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.OrderBookThresholdAdjustments">
            <summary>
            Order book pressure threshold adjustments
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimeParameterSet.Description">
            <summary>
            Description of the parameter set
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdaptationTrigger">
            <summary>
            Adaptation trigger conditions
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptationTrigger.Type">
            <summary>
            Trigger type
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptationTrigger.ThresholdValue">
            <summary>
            Threshold value that triggered adaptation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptationTrigger.CurrentValue">
            <summary>
            Current value that exceeded threshold
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptationTrigger.EvaluationWindow">
            <summary>
            Time window for the trigger evaluation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptationTrigger.Context">
            <summary>
            Additional context for the trigger
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdaptationTriggerType">
            <summary>
            Types of adaptation triggers
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.AdaptationTriggerType.LowWinRate">
            <summary>
            Win rate below threshold
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.AdaptationTriggerType.LowSignalFrequency">
            <summary>
            High win rate but low signal frequency
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.AdaptationTriggerType.RegimeChange">
            <summary>
            Market regime change
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.AdaptationTriggerType.PerformanceDegradation">
            <summary>
            Performance degradation trend
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.AdaptationTriggerType.VolatilitySpike">
            <summary>
            Volatility spike
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.AdaptationTriggerType.Manual">
            <summary>
            Manual trigger
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TriggerEvent">
            <summary>
            Trigger event for monitoring and analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.Timestamp">
            <summary>
            When the trigger occurred
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.TriggerType">
            <summary>
            Type of trigger
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.ThresholdValue">
            <summary>
            Threshold value that was exceeded
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.CurrentValue">
            <summary>
            Current value that triggered the event
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.EvaluationWindow">
            <summary>
            Evaluation window for the trigger
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.MarketData">
            <summary>
            Market data at the time of trigger
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.Context">
            <summary>
            Additional context information
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerEvent.IsUltraLowTimeframe">
            <summary>
            Whether this trigger is appropriate for ultra-low timeframes
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TriggerStatistics">
            <summary>
            Statistics for a specific trigger type
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerStatistics.TriggerType">
            <summary>
            Type of trigger
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerStatistics.TriggerCount">
            <summary>
            Total number of times this trigger has fired
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerStatistics.SuccessfulAdaptations">
            <summary>
            Number of successful adaptations following this trigger
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerStatistics.AverageResponseTime">
            <summary>
            Average time between trigger and adaptation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerStatistics.LastTriggered">
            <summary>
            When this trigger last fired
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TriggerStatistics.SuccessRate">
            <summary>
            Success rate for this trigger type
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.TriggerStatistics.Clone">
            <summary>
            Creates a deep copy of the statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.UltraLowTimeframeCompatibility">
            <summary>
            Ultra-low timeframe compatibility analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeCompatibility.TotalTriggers">
            <summary>
            Total number of triggers analyzed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeCompatibility.UltraLowTimeframeTriggers">
            <summary>
            Number of triggers in ultra-low timeframe windows
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeCompatibility.AppropriateTriggers">
            <summary>
            Number of appropriate triggers for ultra-low timeframes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeCompatibility.CompatibilityScore">
            <summary>
            Compatibility score (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeCompatibility.Issues">
            <summary>
            List of identified compatibility issues
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport">
            <summary>
            Comprehensive monitoring report for adaptive triggers
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.GeneratedAt">
            <summary>
            When this report was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.AnalysisWindow">
            <summary>
            Analysis time window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.TotalTriggerEvents">
            <summary>
            Total number of trigger events
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.TotalAdaptationEvents">
            <summary>
            Total number of adaptation events
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.TriggerStatistics">
            <summary>
            Statistics for each trigger type
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.UltraLowTimeframeCompatibility">
            <summary>
            Ultra-low timeframe compatibility analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.OverallEffectivenessScore">
            <summary>
            Overall effectiveness score (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveTriggerMonitoringReport.Recommendations">
            <summary>
            Optimization recommendations
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport">
            <summary>
            Phase 3 comprehensive analysis report
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport.AnalysisTimestamp">
            <summary>
            When this analysis was performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport.TriggerMonitoringReport">
            <summary>
            Trigger monitoring report
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport.ValidationReport">
            <summary>
            Ultra-low timeframe validation report
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport.TriggerEffectiveness">
            <summary>
            Overall trigger effectiveness score (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport.OverallSystemHealth">
            <summary>
            Overall system health score (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport.Recommendations">
            <summary>
            Comprehensive recommendations for optimization
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3AnalysisReport.RequiresImmediateAction">
            <summary>
            Whether immediate action is required
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.Phase3Recommendation">
            <summary>
            Phase 3 optimization recommendation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3Recommendation.Priority">
            <summary>
            Priority level (CRITICAL, HIGH, MEDIUM, LOW, INFO)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3Recommendation.Category">
            <summary>
            Category of the recommendation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3Recommendation.Issue">
            <summary>
            Description of the issue
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3Recommendation.Recommendation">
            <summary>
            Recommended action
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.Phase3Recommendation.ExpectedImpact">
            <summary>
            Expected impact (High, Medium, Low)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport">
            <summary>
            Ultra-low timeframe validation report
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.ValidationTimestamp">
            <summary>
            When this validation was performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.CurrentSettings">
            <summary>
            Current adaptive settings being validated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.MarketData">
            <summary>
            Market data at time of validation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.ValidationResults">
            <summary>
            Individual validation results
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.OverallScore">
            <summary>
            Overall validation score (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.Recommendations">
            <summary>
            Optimization recommendations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.CriticalIssues">
            <summary>
            Critical issues that need immediate attention
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UltraLowTimeframeValidationReport.OptimizedSettings">
            <summary>
            Optimized settings based on validation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdaptiveValidationResult">
            <summary>
            Adaptive validation result for a specific setting or component
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveValidationResult.Timestamp">
            <summary>
            When this validation was performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveValidationResult.Category">
            <summary>
            Category being validated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveValidationResult.Score">
            <summary>
            Validation score (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveValidationResult.IsPass">
            <summary>
            Whether validation passed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveValidationResult.Message">
            <summary>
            Validation message
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdaptiveValidationResult.Recommendation">
            <summary>
            Recommendation for improvement
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData">
            <summary>
            Tracks performance metrics for individual indicators over time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.IndicatorName">
            <summary>
            Indicator name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.TotalSignals">
            <summary>
            Total number of signals generated by this indicator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.SuccessfulSignals">
            <summary>
            Number of successful signals (profitable trades)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.WinRate">
            <summary>
            Current win rate (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.PerformanceWindows">
            <summary>
            Recent performance over different time windows
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.AverageConfidence">
            <summary>
            Average confidence level of this indicator's signals
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.ReliabilityScore">
            <summary>
            Reliability score (0.0 to 1.0) based on consistency
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.Trend">
            <summary>
            Performance trend (improving, declining, stable)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.LastUpdated">
            <summary>
            Last update timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.History">
            <summary>
            Performance history for trend analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorPerformanceData.RegimePerformance">
            <summary>
            Market regime-specific performance
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.PerformanceWindow">
            <summary>
            Performance data for a specific time window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.Window">
            <summary>
            Time window this data covers
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.SignalCount">
            <summary>
            Number of signals in this window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.SuccessCount">
            <summary>
            Number of successful signals in this window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.WinRate">
            <summary>
            Win rate for this window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.AverageConfidence">
            <summary>
            Average confidence in this window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.WindowStart">
            <summary>
            Window start time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.WindowEnd">
            <summary>
            Window end time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceWindow.PerformanceScore">
            <summary>
            Performance score for this window (weighted by recency)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.PerformanceSnapshot">
            <summary>
            Performance snapshot for trend analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceSnapshot.Timestamp">
            <summary>
            Timestamp of this snapshot
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceSnapshot.WinRate">
            <summary>
            Win rate at this point in time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceSnapshot.Confidence">
            <summary>
            Confidence level at this point
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceSnapshot.SignalCount">
            <summary>
            Number of signals up to this point
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceSnapshot.MarketRegime">
            <summary>
            Market regime at this time
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.RegimePerformance">
            <summary>
            Performance data specific to market regimes
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimePerformance.Regime">
            <summary>
            Market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimePerformance.TotalSignals">
            <summary>
            Total signals in this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimePerformance.SuccessfulSignals">
            <summary>
            Successful signals in this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimePerformance.WinRate">
            <summary>
            Win rate in this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimePerformance.AverageConfidence">
            <summary>
            Average confidence in this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RegimePerformance.PerformanceScore">
            <summary>
            Performance score for this regime
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TimeWindow">
            <summary>
            Time windows for performance tracking
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.TimeWindow.OneHour">
            <summary>
            Last 1 hour of trading
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.TimeWindow.FourHours">
            <summary>
            Last 4 hours of trading
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.TimeWindow.OneDay">
            <summary>
            Last 24 hours of trading
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.TimeWindow.ThreeDays">
            <summary>
            Last 3 days of trading
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.TimeWindow.OneWeek">
            <summary>
            Last 7 days of trading
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.TimeWindow.AllTime">
            <summary>
            All-time performance
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.PerformanceTrend">
            <summary>
            Performance trend indicators
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.PerformanceTrend.Improving">
            <summary>
            Performance is improving over time
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.PerformanceTrend.Declining">
            <summary>
            Performance is declining over time
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.PerformanceTrend.Stable">
            <summary>
            Performance is stable
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.PerformanceTrend.Unknown">
            <summary>
            Not enough data to determine trend
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.SignalOutcome">
            <summary>
            Signal outcome for performance tracking
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.IndicatorName">
            <summary>
            Indicator that generated the signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.Direction">
            <summary>
            Signal direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.Confidence">
            <summary>
            Signal confidence when generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.WasSuccessful">
            <summary>
            Whether the signal was successful (profitable)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.ProfitLoss">
            <summary>
            Profit/loss from this signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.MarketRegime">
            <summary>
            Market regime when signal was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.SignalTime">
            <summary>
            Signal generation timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.OutcomeTime">
            <summary>
            Signal resolution timestamp (when outcome was determined)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.SignalOutcome.Metadata">
            <summary>
            Additional metadata about the signal
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.StrategyPreset">
            <summary>
            Strategy preset definitions
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.StrategyPreset.Name">
            <summary>
            Preset name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.StrategyPreset.Description">
            <summary>
            Preset description
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.StrategyPreset.Preferences">
            <summary>
            User preferences for this preset
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.StrategyPreset.RecommendedRegimes">
            <summary>
            Recommended market conditions for this preset
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.StrategyPreset.IsBuiltIn">
            <summary>
            Whether this is a built-in preset
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.StrategyPreset.Category">
            <summary>
            Preset category
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.StrategyPreset.RiskLevel">
            <summary>
            Risk level (1=Conservative, 5=Aggressive)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria">
            <summary>
            Advanced selection criteria for manual override
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria.BlacklistedIndicators">
            <summary>
            Blacklisted indicators (never select)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria.WhitelistedIndicators">
            <summary>
            Whitelisted indicators (prefer these)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria.MinConfidenceThresholds">
            <summary>
            Minimum confidence thresholds per indicator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria.MaxConfidenceThresholds">
            <summary>
            Maximum confidence thresholds per indicator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria.MinPerformanceThresholds">
            <summary>
            Minimum performance requirements
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria.RequiredRegimes">
            <summary>
            Required market regimes for selection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.AdvancedSelectionCriteria.ExcludedRegimes">
            <summary>
            Excluded market regimes
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MarketData">
            <summary>
            Immutable market data record with comprehensive validation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Timestamp">
            <summary>
            Market data timestamp (UTC)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Price">
            <summary>
            Current price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Volume">
            <summary>
            Trade volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Bid">
            <summary>
            Bid price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Ask">
            <summary>
            Ask price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.BidVolume">
            <summary>
            Bid volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.AskVolume">
            <summary>
            Ask volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Direction">
            <summary>
            Trade direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.DataType">
            <summary>
            Market data type
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.AdditionalData">
            <summary>
            Additional market data properties
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.QualityScore">
            <summary>
            Data quality score (0.0 to 1.0)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.MarketData.Validate">
            <summary>
            Validates the market data integrity
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.Spread">
            <summary>
            Calculates the spread
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.MidPrice">
            <summary>
            Calculates the mid price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketData.SpreadPercentage">
            <summary>
            Calculates the spread percentage
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.MarketData.WithPrice(System.Decimal)">
            <summary>
            Creates a copy with updated price
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.MarketData.WithVolume(System.Int64)">
            <summary>
            Creates a copy with updated volume
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.MarketData.WithTimestamp(System.DateTime)">
            <summary>
            Creates a copy with updated timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TradeDirection">
            <summary>
            Trade direction enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MarketDataType">
            <summary>
            Market data type enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TimeFrame">
            <summary>
            Time frame enumeration for historical data
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TimeRange">
            <summary>
            Time range for data queries
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.TimeRange.#ctor(System.DateTime,System.DateTime)">
            <summary>
            Time range for data queries
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TimeRange.Duration">
            <summary>
            Duration of the time range
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TimeRange.IsValid">
            <summary>
            Validates the time range
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.TimeRange.LastDuration(System.TimeSpan)">
            <summary>
            Creates a time range for the last specified duration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TimeRange.Today">
            <summary>
            Creates a time range for today
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TimeRange.ThisWeek">
            <summary>
            Creates a time range for the current week
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.ExportFormat">
            <summary>
            Export format enumeration for performance data
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MultiTimeframeSignal">
            <summary>
            Multi-timeframe confluence signal with enhanced confidence metrics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MultiTimeframeSignal.ConfluenceMetrics">
            <summary>
            Additional confluence metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.MultiTimeframeSignal.Invalid">
            <summary>
            Creates an invalid multi-timeframe signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.MultiTimeframeSignal.GetConfluenceDescription">
            <summary>
            Gets confluence description for logging
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TimeframeSignal">
            <summary>
            Individual timeframe signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TimeframeSignal.IndicatorReadings">
            <summary>
            Individual indicator contributions for this timeframe
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.PerformanceMetrics">
            <summary>
            Comprehensive performance metrics with real-time updates
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.Name">
            <summary>
            Metric name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.Value">
            <summary>
            Current metric value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.Unit">
            <summary>
            Metric unit (e.g., "ms", "%", "count")
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.Timestamp">
            <summary>
            Metric timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.Aggregations">
            <summary>
            Statistical aggregations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.Tags">
            <summary>
            Metric tags for categorization
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.History">
            <summary>
            Historical values for trend analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.PerformanceMetrics.Metadata">
            <summary>
            Metric metadata
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MetricAggregations">
            <summary>
            Statistical aggregations for metrics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Min">
            <summary>
            Minimum value in the time window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Max">
            <summary>
            Maximum value in the time window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Average">
            <summary>
            Average value in the time window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Median">
            <summary>
            Median value in the time window
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.StandardDeviation">
            <summary>
            Standard deviation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Percentile95">
            <summary>
            95th percentile value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Percentile99">
            <summary>
            99th percentile value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Count">
            <summary>
            Sample count
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MetricAggregations.Window">
            <summary>
            Time window for aggregations
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MetricDataPoint">
            <summary>
            Individual metric data point
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.MetricDataPoint.#ctor(System.Double,System.DateTime,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Individual metric data point
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TradeResult">
            <summary>
            Trade result for performance tracking
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.TradeId">
            <summary>
            Trade identifier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.Direction">
            <summary>
            Trade direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.EntryPrice">
            <summary>
            Entry price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.ExitPrice">
            <summary>
            Exit price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.PositionSize">
            <summary>
            Position size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.EntryTime">
            <summary>
            Trade entry time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.ExitTime">
            <summary>
            Trade exit time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.Duration">
            <summary>
            Trade duration
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.PnL">
            <summary>
            Profit/Loss amount
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.PnLPercentage">
            <summary>
            Profit/Loss percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.IsWinner">
            <summary>
            Whether the trade was profitable
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.Commission">
            <summary>
            Commission paid
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.NetPnL">
            <summary>
            Net profit after commission
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.ExitReason">
            <summary>
            Exit reason
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.OriginalSignalId">
            <summary>
            Original signal that generated this trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradeResult.Metadata">
            <summary>
            Trade metadata
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MarketDepth">
            <summary>
            Market depth data
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.Timestamp">
            <summary>
            Timestamp of the depth data
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.Bids">
            <summary>
            Bid levels (price and volume)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.Asks">
            <summary>
            Ask levels (price and volume)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.BestBid">
            <summary>
            Best bid price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.BestAsk">
            <summary>
            Best ask price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.Spread">
            <summary>
            Current spread
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.MidPrice">
            <summary>
            Mid price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.TotalBidVolume">
            <summary>
            Total bid volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.TotalAskVolume">
            <summary>
            Total ask volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketDepth.ImbalanceRatio">
            <summary>
            Order book imbalance ratio
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.DepthLevel">
            <summary>
            Individual depth level
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.DepthLevel.#ctor(System.Decimal,System.Int64,System.Int32)">
            <summary>
            Individual depth level
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MarketStatistics">
            <summary>
            Market statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.OpenPrice">
            <summary>
            Current session open price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.HighPrice">
            <summary>
            Current session high price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.LowPrice">
            <summary>
            Current session low price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.LastPrice">
            <summary>
            Last traded price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.Volume">
            <summary>
            Total volume traded
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.VWAP">
            <summary>
            Volume-weighted average price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.Volatility">
            <summary>
            Current volatility measure
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.AverageTradeSize">
            <summary>
            Average trade size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.TradeCount">
            <summary>
            Number of trades
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketStatistics.Timestamp">
            <summary>
            Statistics timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TickData">
            <summary>
            Tick data for high-frequency analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TickData.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TickData.Timestamp">
            <summary>
            Tick timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TickData.Price">
            <summary>
            Trade price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TickData.Volume">
            <summary>
            Trade volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TickData.Direction">
            <summary>
            Trade direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TickData.IsAggressive">
            <summary>
            Whether this is an aggressive trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TickData.Conditions">
            <summary>
            Trade conditions/flags
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TradeExitReason">
            <summary>
            Trade exit reason enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TradeConditions">
            <summary>
            Trade conditions flags
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences">
            <summary>
            User preferences for indicator selection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.TrendIndicatorPreference">
            <summary>
            Strategy-level preferences (0.5 to 2.0, where 1.0 = neutral)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.VolumeIndicatorPreference">
            <summary>
            Volume indicator preference multiplier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.MomentumIndicatorPreference">
            <summary>
            Momentum indicator preference multiplier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.OrderFlowIndicatorPreference">
            <summary>
            Order flow indicator preference multiplier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.IndicatorPreferences">
            <summary>
            Individual indicator preferences (0.5 to 2.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.MinConfidenceThresholds">
            <summary>
            Minimum confidence thresholds per indicator (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.SelectionMode">
            <summary>
            Selection mode
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.ManualSelection">
            <summary>
            Manual override selection (when SelectionMode = Manual)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.UserIndicatorPreferences.StrategyPreset">
            <summary>
            Strategy preset name (when SelectionMode = Preset)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorSelectionMode">
            <summary>
            Indicator selection modes
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.IndicatorSelectionMode.SmartAuto">
            <summary>
            Intelligent auto-selection with user influence
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.IndicatorSelectionMode.Manual">
            <summary>
            User manually selects specific indicators
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.Core.Models.IndicatorSelectionMode.Preset">
            <summary>
            Use predefined strategy presets
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorCategory">
            <summary>
            Indicator categories for selection logic
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult">
            <summary>
            Result of indicator selection process
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.SelectedIndicators">
            <summary>
            List of selected indicator names
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.SelectionReason">
            <summary>
            Detailed selection reasoning for transparency
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.SelectionConfidence">
            <summary>
            Confidence in the selection (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.MarketRegime">
            <summary>
            Market regime that influenced the selection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.Timestamp">
            <summary>
            Timestamp of selection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.IndicatorScores">
            <summary>
            Individual indicator scores used in selection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.ProcessingTimeMs">
            <summary>
            Selection processing time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionResult.WasCached">
            <summary>
            Whether this selection was cached
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorScore">
            <summary>
            Individual indicator scoring information
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorScore.IndicatorName">
            <summary>
            Indicator name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorScore.BaseScore">
            <summary>
            Base score from indicator performance
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorScore.RegimeBonus">
            <summary>
            Market regime bonus/penalty
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorScore.UserPreferenceMultiplier">
            <summary>
            User preference multiplier applied
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorScore.FinalScore">
            <summary>
            Final calculated score
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorScore.IsSelected">
            <summary>
            Whether this indicator was selected
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorScore.SelectionReason">
            <summary>
            Reason for selection/rejection
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorSelectionProfile">
            <summary>
            Market regime-specific indicator selection profile
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionProfile.Regime">
            <summary>
            Market regime this profile applies to
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionProfile.CategoryWeights">
            <summary>
            Category weight multipliers for this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionProfile.IndicatorBonuses">
            <summary>
            Specific indicator bonuses for this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionProfile.MinConfidenceThreshold">
            <summary>
            Minimum confidence threshold for this regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionProfile.Description">
            <summary>
            Human-readable description of this profile
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionProfile.PriorityIndicators">
            <summary>
            Priority indicators that should be preferred in this regime
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics">
            <summary>
            Performance metrics for the indicator selection engine
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics.TotalSelections">
            <summary>
            Total number of selections performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics.CachedSelections">
            <summary>
            Number of cached selections served
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics.AverageProcessingTimeMs">
            <summary>
            Average selection processing time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics.MaxProcessingTimeMs">
            <summary>
            Maximum processing time recorded
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics.CacheHitRate">
            <summary>
            Cache hit rate percentage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics.RegimeChanges">
            <summary>
            Number of regime changes detected
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorSelectionPerformanceMetrics.LastReset">
            <summary>
            Last reset timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TradingSignal">
            <summary>
            Immutable trading signal with detailed attribution and confidence scoring
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.Id">
            <summary>
            Unique signal identifier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.Symbol">
            <summary>
            Trading symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.Timestamp">
            <summary>
            Signal generation timestamp (UTC)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.Direction">
            <summary>
            Signal direction (Buy/Sell)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.Confidence">
            <summary>
            Signal strength/confidence (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.EntryPrice">
            <summary>
            Entry price for the signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.StopLoss">
            <summary>
            Stop loss price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.TakeProfit">
            <summary>
            Take profit price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.PositionSize">
            <summary>
            Recommended position size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.Priority">
            <summary>
            Signal priority (higher values = higher priority)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.ExpectedDuration">
            <summary>
            Expected duration of the trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.RiskRewardRatio">
            <summary>
            Risk-reward ratio
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.SourceData">
            <summary>
            Market data that generated this signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.IndicatorContributions">
            <summary>
            Indicators that contributed to this signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.Metadata">
            <summary>
            Signal generation metadata
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.ExpirationTime">
            <summary>
            Signal expiration time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.IsValid">
            <summary>
            Whether the signal is still valid
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.QualityScore">
            <summary>
            Signal quality score based on multiple factors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.TradingSignal.Validate">
            <summary>
            Validates the trading signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.PotentialProfit">
            <summary>
            Calculates the potential profit
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.TradingSignal.PotentialLoss">
            <summary>
            Calculates the potential loss
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.TradingSignal.WithConfidence(System.Double)">
            <summary>
            Creates a copy with updated confidence
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.TradingSignal.WithPositionSize(System.Int32)">
            <summary>
            Creates a copy with updated position size
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.TradingSignal.WithExpiration(System.Nullable{System.DateTime})">
            <summary>
            Creates a copy with updated expiration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.SignalDirection">
            <summary>
            Signal direction enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.IndicatorContribution">
            <summary>
            Indicator contribution to signal generation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorContribution.IndicatorName">
            <summary>
            Indicator name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorContribution.Confidence">
            <summary>
            Indicator confidence in the signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorContribution.Weight">
            <summary>
            Weight of this indicator in the final signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorContribution.Values">
            <summary>
            Indicator-specific values that contributed to the signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.IndicatorContribution.Timestamp">
            <summary>
            Timestamp when indicator was calculated
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.ValidationResult">
            <summary>
            Comprehensive validation result with conflict tracking
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.IsValid">
            <summary>
            Whether validation passed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.Confidence">
            <summary>
            Validation confidence score (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.Errors">
            <summary>
            List of validation errors
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.Warnings">
            <summary>
            List of validation warnings
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.ExecutionTime">
            <summary>
            Validation execution time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.RuleResults">
            <summary>
            Rules that were executed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.Conflicts">
            <summary>
            Detected conflicts between rules
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.Metadata">
            <summary>
            Additional validation metadata
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationResult.Timestamp">
            <summary>
            Validation timestamp
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.ValidationResult.Success(System.Double)">
            <summary>
            Creates a successful validation result
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.ValidationResult.Failure(System.String[])">
            <summary>
            Creates a failed validation result
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Models.ValidationResult.WithWarnings(System.String[])">
            <summary>
            Creates a validation result with warnings
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.BatchValidationResult">
            <summary>
            Batch validation result for multiple signals
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchValidationResult.Results">
            <summary>
            Individual validation results
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchValidationResult.IsValid">
            <summary>
            Overall batch validation success
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchValidationResult.AverageConfidence">
            <summary>
            Average confidence across all validations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchValidationResult.CrossSignalConflicts">
            <summary>
            Cross-signal conflicts detected
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchValidationResult.Stats">
            <summary>
            Batch processing statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchValidationResult.Timestamp">
            <summary>
            Batch validation timestamp
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.RuleValidationResult">
            <summary>
            Individual rule validation result
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.RuleId">
            <summary>
            Rule identifier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.RuleName">
            <summary>
            Rule name
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.Passed">
            <summary>
            Whether rule validation passed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.Confidence">
            <summary>
            Rule confidence in the result
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.ExecutionTime">
            <summary>
            Rule execution time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.ErrorMessage">
            <summary>
            Rule-specific error message
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.WarningMessage">
            <summary>
            Rule-specific warning message
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.OutputValues">
            <summary>
            Rule output values
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.RuleValidationResult.Priority">
            <summary>
            Rule priority level
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.ValidationConflict">
            <summary>
            Validation conflict between rules or signals
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationConflict.Type">
            <summary>
            Conflict type
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationConflict.Severity">
            <summary>
            Conflict severity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationConflict.Description">
            <summary>
            Description of the conflict
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationConflict.InvolvedEntities">
            <summary>
            Entities involved in the conflict
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationConflict.SuggestedResolution">
            <summary>
            Suggested resolution
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationConflict.Timestamp">
            <summary>
            Conflict detection timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.ValidationConflict.AdditionalData">
            <summary>
            Additional conflict data
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.BatchProcessingStats">
            <summary>
            Batch processing statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchProcessingStats.TotalItems">
            <summary>
            Total items processed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchProcessingStats.SuccessfulItems">
            <summary>
            Successfully validated items
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchProcessingStats.FailedItems">
            <summary>
            Failed validation items
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchProcessingStats.TotalProcessingTime">
            <summary>
            Total processing time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchProcessingStats.AverageProcessingTime">
            <summary>
            Average processing time per item
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.BatchProcessingStats.ItemsPerSecond">
            <summary>
            Items processed per second
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.MarketContext">
            <summary>
            Market context for validation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketContext.Regime">
            <summary>
            Current market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketContext.Volatility">
            <summary>
            Current volatility level
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketContext.Session">
            <summary>
            Current trading session
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketContext.Trend">
            <summary>
            Market trend direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketContext.Statistics">
            <summary>
            Current market statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketContext.Timestamp">
            <summary>
            Context timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Models.MarketContext.AdditionalData">
            <summary>
            Additional context data
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.ConflictType">
            <summary>
            Conflict type enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.ConflictSeverity">
            <summary>
            Conflict severity levels
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TradingSession">
            <summary>
            Trading sessions for time validation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.VolatilityLevel">
            <summary>
            Volatility level enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Models.TrendDirection">
            <summary>
            Trend direction enumeration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Presets.StrategyPresetManager">
            <summary>
            Manages strategy presets for smart indicator selection
            Provides built-in and custom preset combinations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.GetAllPresets">
            <summary>
            Gets all available presets
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.GetPreset(System.String)">
            <summary>
            Gets a specific preset by name
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.GetPresetsByCategory(System.String)">
            <summary>
            Gets presets by category
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.GetPresetsForRegime(SmartVolumeStrategy.Core.Models.MarketRegime)">
            <summary>
            Gets presets suitable for a specific market regime
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.SaveCustomPresetAsync(SmartVolumeStrategy.Core.Models.StrategyPreset)">
            <summary>
            Saves a custom preset
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.DeleteCustomPresetAsync(System.String)">
            <summary>
            Deletes a custom preset
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.GetPresetsByCategory">
            <summary>
            Gets preset names by category for UI display
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Presets.StrategyPresetManager.CreatePresetFromPreferences(System.String,System.String,SmartVolumeStrategy.Core.Models.UserIndicatorPreferences,System.String,System.Int32)">
            <summary>
            Creates a preset from current user preferences
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Production.ProductionDeploymentValidator">
            <summary>
            Validates production deployment readiness and environment configuration
            Ensures all components are properly configured for live trading
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Production.ProductionDeploymentValidator.ValidationResult">
            <summary>
            Validation result for deployment checks
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Production.ProductionDeploymentValidator.ValidationSeverity">
            <summary>
            Validation severity levels
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Production.ProductionDeploymentValidator.DeploymentChecklist">
            <summary>
            Production deployment checklist
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Production.ProductionDeploymentValidator.ValidateProductionDeploymentAsync">
            <summary>
            Performs comprehensive production deployment validation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard">
            <summary>
            Production monitoring dashboard for real-time system health and performance tracking
            Consolidates all monitoring data into a unified production-ready interface
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.RealTimeMetrics">
            <summary>
            Real-time metrics for production monitoring
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.AlertConditions">
            <summary>
            Alert conditions for production monitoring
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.GetCurrentMetrics">
            <summary>
            Gets current real-time metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.GetCurrentAlerts">
            <summary>
            Gets current alert conditions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.UpdateIndicatorStatus(System.String,System.Double)">
            <summary>
            Updates indicator status
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.RecordSignal">
            <summary>
            Records a new signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.RecordAdaptation">
            <summary>
            Records an adaptation event
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Production.ProductionMonitoringDashboard.UpdateMarketRegime(SmartVolumeStrategy.Core.Models.MarketRegime)">
            <summary>
            Updates market regime
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.EmergencySignalDetector">
            <summary>
            Emergency signal detection system
            Bypasses normal filtering for extreme market conditions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.EmergencySignalDetector.DetectEmergencyConditions(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Detects emergency market conditions that warrant immediate signal generation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.EmergencySignalDetector.GetStatistics">
            <summary>
            Gets emergency detection statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.EmergencyConditionType">
            <summary>
            Types of emergency conditions that can be detected
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.EmergencyCondition">
            <summary>
            Individual emergency condition detected
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.EmergencySignal">
            <summary>
            Emergency signal generated from detected conditions
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.EmergencyDetectionResult">
            <summary>
            Result of emergency detection analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.EmergencyDetectionStatistics">
            <summary>
            Emergency detection performance statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator">
            <summary>
            Progressive confidence calculation system
            Replaces binary pass/fail with graduated confidence scoring
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculateConfidence(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis,SmartVolumeStrategy.Core.Models.MarketData,System.Collections.Generic.List{System.Object})">
            <summary>
            Calculates progressive confidence using multiple factors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculateRegimeBoost(SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis)">
            <summary>
            Calculates regime-specific confidence boost
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculateMomentumMultiplier(SmartVolumeStrategy.Core.Models.MarketData,SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis)">
            <summary>
            Calculates momentum-based confidence multiplier
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculateStabilityFactor(SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis)">
            <summary>
            Calculates stability-based confidence factor
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculatePhase2Enhancement(System.Collections.Generic.List{System.Object})">
            <summary>
            Calculates Phase 2 signal enhancement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.DetectEmergencyMomentum(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Detects emergency momentum conditions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculateFinalConfidence(SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents)">
            <summary>
            Calculates final confidence from all components
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculatePriceVelocity(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Helper method to calculate price velocity
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculateVolumeSpike(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Helper method to calculate volume spike
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CalculatePriceGap(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Helper method to calculate price gap
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.CreateCalculationReason(SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents)">
            <summary>
            Creates human-readable calculation reason
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceCalculator.LogConfidenceCalculation(SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult)">
            <summary>
            Logs confidence calculation details
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents">
            <summary>
            Individual confidence calculation components
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents.BaseConfidence">
            <summary>
            Base signal confidence component
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents.RegimeBoost">
            <summary>
            Regime-specific boost component
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents.MomentumMultiplier">
            <summary>
            Momentum multiplier component
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents.StabilityFactor">
            <summary>
            Stability factor component
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents.Phase2Enhancement">
            <summary>
            Phase 2 enhancement component
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ConfidenceComponents.EmergencyMomentumBoost">
            <summary>
            Emergency momentum boost component
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult">
            <summary>
            Result of progressive confidence calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.FinalConfidence">
            <summary>
            Final calculated confidence (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.Components">
            <summary>
            Breakdown of confidence components
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.BaseSignal">
            <summary>
            Original base signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.RegimeAnalysis">
            <summary>
            Market regime analysis used
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.MarketData">
            <summary>
            Market data used for calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.CalculationReason">
            <summary>
            Human-readable reason for the confidence level
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.IsEmergencySignal">
            <summary>
            Whether this was triggered by emergency conditions
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.ProgressiveConfidenceResult.Timestamp">
            <summary>
            When this calculation was performed
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalCoordinator">
            <summary>
            Simplified signal coordination system
            Removes complex bottlenecks and focuses on market-adaptive signal generation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalCoordinator.GenerateSignalAsync(SmartVolumeStrategy.Core.Models.MarketData,SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.TradingSignal,System.Threading.CancellationToken)">
            <summary>
            Generates trading signal using simplified, market-adaptive approach
            CRITICAL FIX: Now integrates Enhanced Pressure signals for complete signal coordination
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalCoordinator.GetStatistics">
            <summary>
            Gets performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalCoordinator.IntegrateEnhancedPressureSignal(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Regime.MarketRegimeAnalysis)">
            <summary>
            CRITICAL FIX: Integrates Enhanced Pressure signals with base signals
            Enhanced Pressure signals get priority due to their sophisticated order flow analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult">
            <summary>
            Result of simplified signal coordination
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.Signal">
            <summary>
            Final trading signal generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.IsEmergencySignal">
            <summary>
            Whether this was an emergency signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.RegimeAnalysis">
            <summary>
            Market regime analysis (null for emergency signals)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.ThresholdResult">
            <summary>
            Adaptive threshold result (null for emergency signals)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.ConfidenceResult">
            <summary>
            Progressive confidence result (null for emergency signals)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.EmergencyResult">
            <summary>
            Emergency detection result (null for normal signals)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.ProcessingTimeMs">
            <summary>
            Total processing time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.Timestamp">
            <summary>
            When this result was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedSignalResult.Error">
            <summary>
            Error information if signal generation failed
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics">
            <summary>
            Performance statistics for simplified signal coordinator
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics.TotalSignals">
            <summary>
            Total signals generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics.EmergencySignals">
            <summary>
            Number of emergency signals generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics.RegimeAdaptations">
            <summary>
            Number of regime adaptations performed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics.EmergencySignalRate">
            <summary>
            Rate of emergency signals (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics.RegimeDetectorStats">
            <summary>
            Regime detector stability trend
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics.ThresholdManagerStats">
            <summary>
            Threshold manager statistics
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.SignalGeneration.SimplifiedCoordinatorStatistics.EmergencyDetectorStats">
            <summary>
            Emergency detector statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Utils.CircularBuffer`1">
            <summary>
            Thread-safe circular buffer implementation for efficient data storage with fixed capacity
            Automatically overwrites oldest data when capacity is exceeded
            </summary>
            <typeparam name="T">Type of items to store</typeparam>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.#ctor(System.Int32)">
            <summary>
            Creates a new circular buffer with the specified capacity
            </summary>
            <param name="capacity">Maximum number of items to store</param>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.Capacity">
            <summary>
            Gets the maximum capacity of the buffer
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.Count">
            <summary>
            Gets the current number of items in the buffer
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.IsFull">
            <summary>
            Gets whether the buffer is full
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.IsEmpty">
            <summary>
            Gets whether the buffer is empty
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.Add(`0)">
            <summary>
            Adds an item to the buffer
            If the buffer is full, the oldest item is overwritten
            </summary>
            <param name="item">Item to add</param>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.TryTake(`0@)">
            <summary>
            Tries to remove and return the oldest item from the buffer
            </summary>
            <param name="item">The removed item, or default if buffer is empty</param>
            <returns>True if an item was removed, false if buffer is empty</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.TryPeek(`0@)">
            <summary>
            Tries to peek at the oldest item without removing it
            </summary>
            <param name="item">The oldest item, or default if buffer is empty</param>
            <returns>True if an item was found, false if buffer is empty</returns>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.Item(System.Int32)">
            <summary>
            Gets the item at the specified index (0 = oldest, Count-1 = newest)
            </summary>
            <param name="index">Index of the item to get</param>
            <returns>The item at the specified index</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.Clear">
            <summary>
            Clears all items from the buffer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.ToArray">
            <summary>
            Converts the buffer contents to an array (oldest to newest)
            </summary>
            <returns>Array containing all items in the buffer</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.Where(System.Func{`0,System.Boolean})">
            <summary>
            Gets items that match the specified predicate
            </summary>
            <param name="predicate">Predicate to match items</param>
            <returns>Enumerable of matching items</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.LastOrDefault">
            <summary>
            Gets the last item added to the buffer (newest)
            </summary>
            <returns>The newest item, or default if buffer is empty</returns>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the buffer (oldest to newest)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the buffer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Utils.CircularBuffer`1.Dispose">
            <summary>
            Disposes the circular buffer and clears all references
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine">
            <summary>
            Hierarchical validation engine with priority-based rule execution and conflict resolution
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.ValidateSignalAsync(SmartVolumeStrategy.Core.Models.TradingSignal,System.Threading.CancellationToken)">
            <summary>
            Validates a trading signal through the hierarchical rule system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.ValidateMarketDataAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Validates market data for trading readiness
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.GetStatistics">
            <summary>
            Gets current validation statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.ExecuteRuleAsync(SmartVolumeStrategy.Core.Interfaces.IValidationRule,SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Executes a single validation rule
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.DetectConflicts(System.Collections.Generic.List{SmartVolumeStrategy.Core.Validation.RuleValidationResult})">
            <summary>
            Detects conflicts between validation rule results
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.ResolveConflicts(System.Collections.Generic.List{SmartVolumeStrategy.Core.Models.ValidationConflict},System.Collections.Generic.List{SmartVolumeStrategy.Core.Validation.RuleValidationResult})">
            <summary>
            Resolves conflicts between validation rules
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.AggregateResults(System.Collections.Generic.List{SmartVolumeStrategy.Core.Validation.RuleValidationResult})">
            <summary>
            Aggregates individual rule results into final validation result
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.GetRuleWeight(System.Int32)">
            <summary>
            Gets weight for a rule based on its priority
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.UpdateAverageTime(System.Double,System.Double,System.Int32)">
            <summary>
            Updates running average time
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.HierarchicalValidationEngine.InitializeRulePriorities">
            <summary>
            Initializes rule priority mappings
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.ValidationStatistics">
            <summary>
            Internal validation statistics tracking
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.RuleValidationResult">
            <summary>
            Result of individual rule validation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule">
            <summary>
            Validates risk management requirements including risk-reward ratios and position sizing
            High priority validation rule
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.Priority">
            <summary>
            Priority level (2 = High)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.Name">
            <summary>
            Rule name for identification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.ValidateAsync(SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates risk management requirements
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.ValidateRiskRewardRatio(SmartVolumeStrategy.Core.Models.TradingSignal)">
            <summary>
            Validates minimum risk-reward ratio
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.ValidatePositionSize(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Validates position size based on confidence and risk parameters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.ValidatePortfolioExposure(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Validates portfolio exposure limits
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.ValidateCorrelationLimits(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Validates correlation-based position limits
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.GetCurrentPortfolioExposure(SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Gets current portfolio exposure
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.RiskValidationRule.GetCorrelatedPositions(System.String,SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Gets positions in instruments correlated with the given symbol
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters">
            <summary>
            Risk management parameters
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.MinRiskRewardRatio">
            <summary>
            Minimum risk-reward ratio required
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.BasePositionSize">
            <summary>
            Base position size for normal confidence signals
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.MaxPositionSize">
            <summary>
            Maximum position size allowed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.MinPositionSize">
            <summary>
            Minimum position size allowed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.MaxPortfolioExposure">
            <summary>
            Maximum total portfolio exposure
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.MaxCorrelatedExposure">
            <summary>
            Maximum exposure to correlated instruments
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.MaxRiskPerTradePercent">
            <summary>
            Maximum percentage of account to risk per trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.RiskParameters.MaxDailyLossPercent">
            <summary>
            Maximum daily loss percentage
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.Position">
            <summary>
            Represents a trading position for risk calculations
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.MomentumValidationRule">
            <summary>
            Validates momentum requirements for signal quality
            Medium priority validation rule
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.MomentumValidationRule.Priority">
            <summary>
            Priority level (3 = Medium)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.MomentumValidationRule.Name">
            <summary>
            Rule name for identification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.MomentumValidationRule.ValidateAsync(SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates momentum requirements
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.MomentumValidationRule.GetMomentumScore(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Gets or calculates momentum score for the signal
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.CorrelationValidationRule">
            <summary>
            Validates correlation limits to prevent over-concentration
            Normal priority validation rule
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.CorrelationValidationRule.Priority">
            <summary>
            Priority level (4 = Normal)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.CorrelationValidationRule.Name">
            <summary>
            Rule name for identification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.CorrelationValidationRule.ValidateAsync(SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates correlation limits
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.CorrelationValidationRule.GetCorrelatedInstruments(System.String)">
            <summary>
            Gets instruments correlated with the given symbol
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.CorrelationValidationRule.InitializeCorrelationGroups">
            <summary>
            Initializes correlation groups
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule">
            <summary>
            Validates time-based trading conditions including market hours and session filtering
            High priority validation rule
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.Priority">
            <summary>
            Priority level (2 = High)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.Name">
            <summary>
            Rule name for identification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.ValidateAsync(SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates time-based trading conditions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.ValidateMarketHours(System.String,System.DateTime)">
            <summary>
            Validates that current time is within market hours
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.ValidateTradingSession(System.String,System.DateTime)">
            <summary>
            Validates trading session quality
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.ValidateExcludedDays(System.DateTime)">
            <summary>
            Validates excluded days (weekends for traditional markets)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.ValidateNewsEvents(System.String,System.DateTime)">
            <summary>
            Validates against high-impact news events
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.GetMarketHours(System.String)">
            <summary>
            Gets market hours for a specific symbol
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.DetermineTradingSession(System.DateTime)">
            <summary>
            Determines current trading session based on time
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.IsVolatileInstrument(System.String)">
            <summary>
            Checks if instrument is considered volatile
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.TimeValidationRule.InitializeMarketHours">
            <summary>
            Initializes market hours for different instruments
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.MarketHours">
            <summary>
            Market hours configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.TimeRange">
            <summary>
            Time range for blackout periods
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule">
            <summary>
            Validates minimum volume thresholds based on timeframe and market conditions
            Critical priority validation rule
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.Priority">
            <summary>
            Priority level (1 = Critical)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.Name">
            <summary>
            Rule name for identification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.ValidateAsync(SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates volume requirements for the trading signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.GetMinimumVolumeThreshold(SmartVolumeStrategy.Core.Models.TimeFrame)">
            <summary>
            Gets minimum volume threshold for the specified timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.DetermineTimeFrame(SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Determines the current timeframe from context
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.DetermineMarketRegime(SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Determines the current market regime from context
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.ValidateVolumeSpike(SmartVolumeStrategy.Core.Models.MarketData,SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Validates against volume spikes that might indicate manipulation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.VolumeValidationRule.ValidateVolumeConsistency(SmartVolumeStrategy.Core.Models.MarketData,SmartVolumeStrategy.Core.Interfaces.ValidationContext)">
            <summary>
            Validates volume consistency over recent periods
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.Core.Validation.Rules.SpreadValidationRule">
            <summary>
            Validates spread requirements for ultra-low timeframe scalping
            Critical priority validation rule
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.SpreadValidationRule.Priority">
            <summary>
            Priority level (1 = Critical)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.Core.Validation.Rules.SpreadValidationRule.Name">
            <summary>
            Rule name for identification
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.Core.Validation.Rules.SpreadValidationRule.ValidateAsync(SmartVolumeStrategy.Core.Interfaces.ValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates bid-ask spread requirements
            </summary>
        </member>
    </members>
</doc>
