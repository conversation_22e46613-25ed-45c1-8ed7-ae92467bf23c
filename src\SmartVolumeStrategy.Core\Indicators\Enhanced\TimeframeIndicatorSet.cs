using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Models;
using SmartVolumeStrategy.Core.Indicators.Tier1;
using SmartVolumeStrategy.Core.Indicators.Tier2;
using SmartVolumeStrategy.Core.Infrastructure.MemoryManagement;

namespace SmartVolumeStrategy.Core.Indicators.Enhanced;

/// <summary>
/// Manages a complete set of indicators for a specific timeframe
/// Optimized for ultra-low timeframe scalping with timeframe-specific parameters
/// </summary>
public class TimeframeIndicatorSet
{
    private readonly TimeSpan _timeframe;
    private readonly ILogger _logger;
    private readonly object _updateLock = new();
    
    // Timeframe-specific indicators
    private readonly VolumeFlowIndex _volumeFlowIndex;
    private readonly UltraFastRSI _ultraFastRSI;
    private readonly BollingerBands _bollingerBands;
    private readonly StochasticOscillator _stochasticOscillator;
    
    // Timeframe data management
    private readonly CircularBuffer<MarketData> _timeframeData;
    private DateTime _lastBarTime = DateTime.MinValue;
    private MarketData? _currentBarData;
    
    // Performance tracking
    private long _totalUpdates = 0;
    private double _averageUpdateTimeMs = 0;

    public TimeframeIndicatorSet(TimeSpan timeframe, ILogger logger)
    {
        _timeframe = timeframe;
        _logger = logger;
        
        // Initialize timeframe data buffer
        var bufferSize = CalculateBufferSize(timeframe);
        _timeframeData = new CircularBuffer<MarketData>(bufferSize);
        
        // Initialize indicators with timeframe-optimized parameters
        var config = GetTimeframeOptimizedConfig(timeframe);

        // CRITICAL FIX: Create proper typed loggers for each indicator
        var vfiLogger = CreateTypedLogger<VolumeFlowIndex>(logger);
        var rsiLogger = CreateTypedLogger<UltraFastRSI>(logger);
        var bbLogger = CreateTypedLogger<BollingerBands>(logger);
        var stochLogger = CreateTypedLogger<StochasticOscillator>(logger);

        _volumeFlowIndex = new VolumeFlowIndex(
            vfiLogger,
            period: config.VFIPeriod,
            buyThreshold: config.VFIBuyThreshold,
            sellThreshold: config.VFISellThreshold);

        _ultraFastRSI = new UltraFastRSI(
            rsiLogger,
            period: config.RSIPeriod,
            overboughtThreshold: config.RSIOverbought,
            oversoldThreshold: config.RSIOversold);

        _bollingerBands = new BollingerBands(
            bbLogger,
            period: config.BBPeriod,
            standardDeviations: config.BBStdDev);

        _stochasticOscillator = new StochasticOscillator(
            stochLogger,
            kPeriod: config.StochKPeriod,
            dPeriod: config.StochDPeriod,
            overboughtThreshold: config.StochOverbought,
            oversoldThreshold: config.StochOversold);
    }

    /// <summary>
    /// Updates all indicators with new market data
    /// </summary>
    public void UpdateIndicators(MarketData marketData)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            lock (_updateLock)
            {
                _totalUpdates++;
                
                // Convert to timeframe bar if needed
                var timeframeData = ConvertToTimeframeData(marketData);
                if (timeframeData == null)
                    return; // No new bar to process
                
                // Update all indicators
                _volumeFlowIndex.Update(timeframeData);
                _ultraFastRSI.Update(timeframeData);
                _bollingerBands.Update(timeframeData);
                _stochasticOscillator.Update(timeframeData);
                
                // Track performance
                var processingTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                UpdatePerformanceMetrics(processingTime);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error updating timeframe {Timeframe} indicators", _timeframe);
        }
    }

    /// <summary>
    /// Generates signal from all indicators in this timeframe
    /// </summary>
    public TimeframeSignal GenerateSignal(MarketData marketData)
    {
        try
        {
            var indicatorReadings = new Dictionary<string, IndicatorReading>
            {
                ["VolumeFlowIndex"] = _volumeFlowIndex.GetCurrentReading(),
                ["UltraFastRSI"] = _ultraFastRSI.GetCurrentReading(),
                ["BollingerBands"] = _bollingerBands.GetCurrentReading(),
                ["StochasticOscillator"] = _stochasticOscillator.GetCurrentReading()
            };
            
            // Filter valid readings
            var validReadings = indicatorReadings.Values
                .Where(r => r.IsValid && r.Confidence > 0.3)
                .ToList();
            
            if (!validReadings.Any())
            {
                return new TimeframeSignal
                {
                    Direction = SignalDirection.None,
                    Confidence = 0.0,
                    IsValid = false,
                    Timestamp = marketData.Timestamp
                };
            }
            
            // Calculate weighted signal
            var (direction, confidence) = CalculateWeightedSignal(validReadings);
            
            return new TimeframeSignal
            {
                Direction = direction,
                Confidence = confidence,
                Timeframe = _timeframe,
                IsValid = confidence > 0.4, // Minimum confidence threshold
                Timestamp = marketData.Timestamp,
                IndicatorReadings = indicatorReadings
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error generating signal for timeframe {Timeframe}", _timeframe);
            return new TimeframeSignal { IsValid = false, Timestamp = marketData.Timestamp };
        }
    }

    /// <summary>
    /// Converts tick data to timeframe bars
    /// </summary>
    private MarketData? ConvertToTimeframeData(MarketData tickData)
    {
        var barStartTime = GetBarStartTime(tickData.Timestamp, _timeframe);
        
        // Check if we need to start a new bar
        if (barStartTime > _lastBarTime)
        {
            // Complete previous bar if exists
            MarketData? completedBar = null;
            if (_currentBarData != null)
            {
                completedBar = _currentBarData;
                _timeframeData.Add(completedBar);
            }
            
            // Start new bar
            _lastBarTime = barStartTime;
            _currentBarData = new MarketData
            {
                Symbol = tickData.Symbol,
                Timestamp = barStartTime,
                Price = tickData.Price,
                Volume = tickData.Volume,
                Bid = tickData.Bid,
                Ask = tickData.Ask,
                BidVolume = tickData.BidVolume,
                AskVolume = tickData.AskVolume,
                Direction = tickData.Direction,
                DataType = tickData.DataType,
                QualityScore = tickData.QualityScore
            };
            
            return completedBar; // Return completed bar for processing
        }
        else
        {
            // Update current bar
            if (_currentBarData != null)
            {
                _currentBarData = new MarketData
                {
                    Symbol = _currentBarData.Symbol,
                    Timestamp = _currentBarData.Timestamp,
                    Price = tickData.Price, // Use latest price as close
                    Volume = _currentBarData.Volume + tickData.Volume,
                    Bid = tickData.Bid,
                    Ask = tickData.Ask,
                    BidVolume = _currentBarData.BidVolume + tickData.BidVolume,
                    AskVolume = _currentBarData.AskVolume + tickData.AskVolume,
                    Direction = tickData.Direction,
                    DataType = _currentBarData.DataType,
                    QualityScore = Math.Max(_currentBarData.QualityScore, tickData.QualityScore)
                };
            }
            
            return null; // No completed bar yet
        }
    }

    /// <summary>
    /// Calculates weighted signal from multiple indicators
    /// </summary>
    private (SignalDirection direction, double confidence) CalculateWeightedSignal(List<IndicatorReading> readings)
    {
        // Indicator weights for timeframe-specific analysis
        var weights = GetIndicatorWeights();
        
        var buyWeight = 0.0;
        var sellWeight = 0.0;
        var totalWeight = 0.0;
        
        foreach (var reading in readings)
        {
            if (weights.TryGetValue(reading.IndicatorName, out var weight))
            {
                var weightedConfidence = reading.Confidence * weight;
                totalWeight += weight;
                
                switch (reading.Direction)
                {
                    case SignalDirection.Buy:
                        buyWeight += weightedConfidence;
                        break;
                    case SignalDirection.Sell:
                        sellWeight += weightedConfidence;
                        break;
                }
            }
        }
        
        if (totalWeight == 0)
            return (SignalDirection.None, 0.0);
        
        // Determine dominant direction
        if (buyWeight > sellWeight)
        {
            return (SignalDirection.Buy, buyWeight / totalWeight);
        }
        else if (sellWeight > buyWeight)
        {
            return (SignalDirection.Sell, sellWeight / totalWeight);
        }
        else
        {
            return (SignalDirection.None, 0.0);
        }
    }

    /// <summary>
    /// Gets timeframe-optimized configuration
    /// </summary>
    private TimeframeIndicatorConfig GetTimeframeOptimizedConfig(TimeSpan timeframe)
    {
        // Optimize parameters based on timeframe
        if (timeframe.TotalSeconds <= 15)
        {
            // Ultra-fast for 15s timeframe
            return new TimeframeIndicatorConfig
            {
                VFIPeriod = 1, VFIBuyThreshold = 60m, VFISellThreshold = 40m,
                RSIPeriod = 1, RSIOverbought = 75m, RSIOversold = 25m,
                BBPeriod = 5, BBStdDev = 1.5m,
                StochKPeriod = 3, StochDPeriod = 1, StochOverbought = 70m, StochOversold = 30m
            };
        }
        else if (timeframe.TotalSeconds <= 30)
        {
            // Fast for 30s timeframe
            return new TimeframeIndicatorConfig
            {
                VFIPeriod = 2, VFIBuyThreshold = 62m, VFISellThreshold = 38m,
                RSIPeriod = 2, RSIOverbought = 70m, RSIOversold = 30m,
                BBPeriod = 8, BBStdDev = 1.8m,
                StochKPeriod = 5, StochDPeriod = 2, StochOverbought = 75m, StochOversold = 25m
            };
        }
        else if (timeframe.TotalMinutes <= 1)
        {
            // Standard for 1min timeframe
            return new TimeframeIndicatorConfig
            {
                VFIPeriod = 3, VFIBuyThreshold = 65m, VFISellThreshold = 35m,
                RSIPeriod = 3, RSIOverbought = 70m, RSIOversold = 30m,
                BBPeriod = 10, BBStdDev = 2.0m,
                StochKPeriod = 7, StochDPeriod = 3, StochOverbought = 80m, StochOversold = 20m
            };
        }
        else
        {
            // Slower for 2min+ timeframe
            return new TimeframeIndicatorConfig
            {
                VFIPeriod = 5, VFIBuyThreshold = 70m, VFISellThreshold = 30m,
                RSIPeriod = 5, RSIOverbought = 75m, RSIOversold = 25m,
                BBPeriod = 15, BBStdDev = 2.2m,
                StochKPeriod = 10, StochDPeriod = 3, StochOverbought = 80m, StochOversold = 20m
            };
        }
    }

    /// <summary>
    /// Gets indicator weights for this timeframe
    /// </summary>
    private Dictionary<string, double> GetIndicatorWeights()
    {
        return new Dictionary<string, double>
        {
            ["VolumeFlowIndex"] = 0.30,      // Highest weight for volume analysis
            ["BollingerBands"] = 0.25,       // Price action confirmation
            ["StochasticOscillator"] = 0.25,  // Momentum confirmation
            ["UltraFastRSI"] = 0.20          // Additional momentum
        };
    }

    /// <summary>
    /// CRITICAL FIX: Creates a properly typed logger for indicator constructors
    /// Prevents null logger issues that cause indicator initialization failures
    /// </summary>
    private ILogger<T> CreateTypedLogger<T>(ILogger baseLogger)
    {
        try
        {
            // Try to cast to the specific type first
            if (baseLogger is ILogger<T> typedLogger)
                return typedLogger;

            // If that fails, create a wrapper logger
            return new LoggerWrapper<T>(baseLogger);
        }
        catch (Exception)
        {
            // Last resort: create a null logger to prevent crashes
            return new NullLogger<T>();
        }
    }

    /// <summary>
    /// Calculates buffer size based on timeframe
    /// </summary>
    private int CalculateBufferSize(TimeSpan timeframe)
    {
        // Buffer size to hold enough data for longest indicator period
        var maxPeriod = 20; // Maximum period we might need
        return Math.Max(maxPeriod * 2, 50); // At least 50 bars
    }

    /// <summary>
    /// Gets bar start time for timeframe
    /// </summary>
    private DateTime GetBarStartTime(DateTime timestamp, TimeSpan timeframe)
    {
        var totalSeconds = (long)timeframe.TotalSeconds;
        var epochSeconds = ((DateTimeOffset)timestamp).ToUnixTimeSeconds();
        var barStartSeconds = (epochSeconds / totalSeconds) * totalSeconds;
        return DateTimeOffset.FromUnixTimeSeconds(barStartSeconds).DateTime;
    }

    /// <summary>
    /// Updates performance metrics
    /// </summary>
    private void UpdatePerformanceMetrics(double processingTimeMs)
    {
        _averageUpdateTimeMs = (_averageUpdateTimeMs * (_totalUpdates - 1) + processingTimeMs) / _totalUpdates;
        
        if (processingTimeMs > 5.0)
        {
            _logger.LogWarning("⚠️ Timeframe {Timeframe} update took {ProcessingTime:F2}ms (target: <5ms)", 
                _timeframe, processingTimeMs);
        }
    }
}

/// <summary>
/// Timeframe-specific indicator configuration
/// </summary>
public class TimeframeIndicatorConfig
{
    // VFI Configuration
    public int VFIPeriod { get; set; }
    public decimal VFIBuyThreshold { get; set; }
    public decimal VFISellThreshold { get; set; }

    // RSI Configuration
    public int RSIPeriod { get; set; }
    public decimal RSIOverbought { get; set; }
    public decimal RSIOversold { get; set; }

    // Bollinger Bands Configuration
    public int BBPeriod { get; set; }
    public decimal BBStdDev { get; set; }

    // Stochastic Configuration
    public int StochKPeriod { get; set; }
    public int StochDPeriod { get; set; }
    public decimal StochOverbought { get; set; }
    public decimal StochOversold { get; set; }
}

/// <summary>
/// CRITICAL FIX: Logger wrapper that adapts a generic ILogger to ILogger of T
/// Prevents null logger issues during indicator initialization
/// </summary>
public class LoggerWrapper<T> : ILogger<T>
{
    private readonly ILogger _baseLogger;

    public LoggerWrapper(ILogger baseLogger)
    {
        _baseLogger = baseLogger ?? throw new ArgumentNullException(nameof(baseLogger));
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return _baseLogger.BeginScope(state);
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        return _baseLogger.IsEnabled(logLevel);
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        _baseLogger.Log(logLevel, eventId, state, exception, formatter);
    }
}

/// <summary>
/// CRITICAL FIX: Null logger implementation for ILogger of T
/// Used as last resort when all other logger creation methods fail
/// </summary>
public class NullLogger<T> : ILogger<T>
{
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return null;
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        return false;
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        // Do nothing - null logger
    }
}
