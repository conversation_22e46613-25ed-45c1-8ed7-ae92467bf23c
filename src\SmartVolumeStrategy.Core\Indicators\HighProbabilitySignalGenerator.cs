using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Indicators.Tier1;
using SmartVolumeStrategy.Core.Indicators.Tier2;
using SmartVolumeStrategy.Core.Indicators.Optimization;
using SmartVolumeStrategy.Core.Interfaces;
using SmartVolumeStrategy.Core.Models;

namespace SmartVolumeStrategy.Core.Indicators;

/// <summary>
/// High-Probability Signal Generator - Combines all 7 indicators for signal generation
/// Requires 5 or more indicators aligned with 0.85 or higher confidence threshold
/// </summary>
public class HighProbabilitySignalGenerator : ISignalGenerator, IDisposable
{
    private readonly ILogger<HighProbabilitySignalGenerator> _logger;
    private readonly List<IIndicator> _indicators;
    private readonly Dictionary<string, double> _indicatorWeights;
    private readonly ISmartIndicatorSelectionEngine? _selectionEngine;
    private double _confidenceThreshold;
    private int _requiredIndicatorAlignment;
    private bool _disposed;

    // Smart selection configuration
    private UserIndicatorPreferences _userPreferences;
    private bool _useSmartSelection;

    // Performance tracking
    private readonly List<double> _processingTimes = new();
    private int _totalSignalsGenerated;
    private int _highConfidenceSignals;
    private DateTime _startTime;

    // CRITICAL DEBUG: Threshold optimization for ultra-low timeframe demo data
    private readonly IndicatorThresholdOptimizer _thresholdOptimizer;

    /// <summary>
    /// Initializes a new High-Probability Signal Generator
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="volumeFlowIndex">Volume Flow Index indicator</param>
    /// <param name="orderBookPressure">Order Book Pressure Ratio indicator</param>
    /// <param name="vwapDeviation">VWAP Deviation Tracker indicator</param>
    /// <param name="largeOrderDetection">Large Order Detection indicator</param>
    /// <param name="bollingerBands">Bollinger Bands indicator</param>
    /// <param name="stochasticOscillator">Stochastic Oscillator indicator</param>
    /// <param name="ultraFastRSI">Ultra-Fast RSI indicator</param>
    /// <param name="tickBasedMACD">Tick-Based MACD indicator</param>
    /// <param name="liquidityGapAnalysis">Liquidity Gap Analysis indicator</param>
    /// <param name="selectionEngine">Optional smart indicator selection engine for adaptive indicator selection</param>
    public HighProbabilitySignalGenerator(
        ILogger<HighProbabilitySignalGenerator> logger,
        VolumeFlowIndex volumeFlowIndex,
        OrderBookPressureRatio orderBookPressure,
        VWAPDeviationTracker vwapDeviation,
        LargeOrderDetection largeOrderDetection,
        BollingerBands bollingerBands,
        StochasticOscillator stochasticOscillator,
        UltraFastRSI ultraFastRSI,
        TickBasedMACD tickBasedMACD,
        LiquidityGapAnalysis liquidityGapAnalysis,
        ISmartIndicatorSelectionEngine? selectionEngine = null)
    {
        _logger = logger;
        _selectionEngine = selectionEngine;

        // Production configuration: High-probability signal generation with 85% confidence threshold
        _confidenceThreshold = 0.85; // Production target for high-probability signals
        _logger.LogInformation($"✅ PRODUCTION: Core confidence threshold set to {_confidenceThreshold:P0} for high-probability signals");
        // Default value - will be dynamically adjusted by user configuration and adaptive system
        _requiredIndicatorAlignment = 3; // Conservative default, will be overridden by user configuration
        _logger.LogInformation($"✅ PRODUCTION: Required indicator alignment set to {_requiredIndicatorAlignment}+ (default - will be configured by user settings)");
        _startTime = DateTime.UtcNow;

        // Initialize smart selection configuration
        _useSmartSelection = _selectionEngine != null;
        _userPreferences = new UserIndicatorPreferences(); // Default preferences

        if (_useSmartSelection)
        {
            _logger.LogInformation("🎯 Smart Indicator Selection ENABLED - Intelligent adaptive selection active");
        }
        else
        {
            _logger.LogInformation("📊 Smart Indicator Selection DISABLED - Using traditional fixed selection");
        }

        // Initialize indicator collection
        _indicators = new List<IIndicator>
        {
            volumeFlowIndex,
            orderBookPressure,
            vwapDeviation,
            largeOrderDetection,
            bollingerBands,
            stochasticOscillator,
            ultraFastRSI,
            tickBasedMACD,
            liquidityGapAnalysis
        };

        // Define indicator weights based on tier and specification
        // Tier 1 (85%): 6 indicators = ~14.2% each, Tier 2 (15%): 3 indicators = ~5% each
        _indicatorWeights = new Dictionary<string, double>
        {
            ["VolumeFlowIndex"] = 0.20,        // Tier 1 (reduced from 0.25)
            ["OrderBookPressureRatio"] = 0.20, // Tier 1 (reduced from 0.25)
            ["VWAPDeviationTracker"] = 0.15,   // Tier 1 (reduced from 0.20)
            ["LargeOrderDetection"] = 0.15,    // Tier 1 (unchanged)
            ["BollingerBands"] = 0.15,         // Tier 1 (new)
            ["StochasticOscillator"] = 0.15,   // Tier 1 (new)
            ["UltraFastRSI"] = 0.08,          // Tier 2 (unchanged)
            ["TickBasedMACD"] = 0.07,         // Tier 2 (unchanged)
            ["LiquidityGapAnalysis"] = 0.05    // Tier 2 (unchanged)
        };

        // CRITICAL DEBUG: Initialize threshold optimizer for ultra-low timeframe demo data
        var optimizerLogger = new LoggerFactory().CreateLogger<IndicatorThresholdOptimizer>();
        _thresholdOptimizer = new IndicatorThresholdOptimizer(optimizerLogger, analysisWindow: 100);

        _logger.LogInformation("Initialized HighProbabilitySignalGenerator with {IndicatorCount} indicators",
            _indicators.Count);
        _logger.LogInformation("CRITICAL: Restored proper indicator alignment requirement to {RequiredAlignment} indicators",
            _requiredIndicatorAlignment);
        _logger.LogInformation("Confidence threshold set to {ConfidenceThreshold:P2} for high-probability signals",
            _confidenceThreshold);
        _logger.LogInformation("🔧 THRESHOLD OPTIMIZER: Enabled for ultra-low timeframe demo data analysis");
    }

    /// <inheritdoc />
    public double ConfidenceThreshold => _confidenceThreshold;

    /// <inheritdoc />
    public event EventHandler<TradingSignal>? HighConfidenceSignalGenerated;

    /// <inheritdoc />
    public event EventHandler<SignalGenerationStats>? StatisticsUpdated;

    /// <inheritdoc />
    public async Task<TradingSignal> GenerateSignalAsync(MarketData marketData, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // Update all indicators with new market data
            var indicatorReadings = await UpdateIndicatorsAsync(marketData, cancellationToken);

            // Apply smart indicator selection if enabled
            List<IndicatorReading> selectedReadings;
            IndicatorSelectionResult? selectionResult = null;

            if (_useSmartSelection && _selectionEngine != null)
            {
                // Update selection engine with market data
                _selectionEngine.UpdateMarketData(marketData);

                // Perform smart selection
                selectionResult = _selectionEngine.SelectIndicators(
                    marketData,
                    indicatorReadings,
                    _requiredIndicatorAlignment,
                    _userPreferences);

                // Filter readings to only selected indicators
                selectedReadings = indicatorReadings
                    .Where(r => selectionResult.SelectedIndicators.Contains(r.IndicatorName))
                    .ToList();

                // Log smart selection transparency
                _logger.LogInformation("🎯 Smart Selection: {Regime} market detected", selectionResult.MarketRegime);
                _logger.LogInformation("📊 Selected Indicators: {Indicators}",
                    string.Join(", ", selectionResult.SelectedIndicators));
                _logger.LogInformation("💡 Reason: {Reason}", selectionResult.SelectionReason);
            }
            else
            {
                // Use traditional selection (all valid indicators)
                selectedReadings = indicatorReadings;
            }

            // Count aligned indicators from selection
            var alignedCount = CountAlignedIndicators(selectedReadings);

            // Check if we have enough aligned indicators
            if (alignedCount < _requiredIndicatorAlignment)
            {
                var reason = _useSmartSelection
                    ? $"Smart selection insufficient alignment ({alignedCount}/{_requiredIndicatorAlignment})"
                    : "Insufficient indicator alignment";
                return CreateNoSignal(marketData, indicatorReadings, reason);
            }

            // Calculate weighted confidence using selected indicators
            var confidence = CalculateWeightedConfidence(selectedReadings);

            // Check confidence threshold
            if (confidence < _confidenceThreshold)
            {
                return CreateNoSignal(marketData, indicatorReadings, "Confidence below threshold");
            }

            // Determine signal direction using selected indicators
            var direction = DetermineSignalDirection(selectedReadings);
            if (direction == SignalDirection.None)
            {
                return CreateNoSignal(marketData, indicatorReadings, "No clear direction consensus");
            }

            // Create high-confidence signal with selection metadata
            var signal = CreateHighConfidenceSignal(marketData, selectedReadings, direction, confidence, alignedCount);

            // Add smart selection metadata
            if (selectionResult != null)
            {
                signal.Metadata["SmartSelectionUsed"] = true;
                signal.Metadata["SelectionReason"] = selectionResult.SelectionReason;
                signal.Metadata["MarketRegime"] = selectionResult.MarketRegime.ToString();
                signal.Metadata["SelectionConfidence"] = selectionResult.SelectionConfidence;
            }

            // Update statistics
            _totalSignalsGenerated++;
            _highConfidenceSignals++;
            TrackProcessingTime(startTime);

            // Fire event for high-confidence signal
            HighConfidenceSignalGenerated?.Invoke(this, signal);

            var logMessage = _useSmartSelection
                ? $"Generated SMART signal: {direction} with confidence {confidence:F3} ({alignedCount} selected indicators)"
                : $"Generated signal: {direction} with confidence {confidence:F3} ({alignedCount} indicators)";
            _logger.LogDebug(logMessage);

            return signal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating signal for market data at {Timestamp}", marketData.Timestamp);
            return CreateErrorSignal(marketData, ex.Message);
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TradingSignal>> GenerateSignalBatchAsync(
        IEnumerable<MarketData> marketDataBatch, 
        CancellationToken cancellationToken = default)
    {
        var signals = new List<TradingSignal>();
        
        foreach (var marketData in marketDataBatch)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            var signal = await GenerateSignalAsync(marketData, cancellationToken);
            signals.Add(signal);
        }

        return signals;
    }

    /// <inheritdoc />
    public void UpdateMarketData(MarketData marketData)
    {
        // Synchronous update for real-time processing
        foreach (var indicator in _indicators)
        {
            if (indicator.IsEnabled)
            {
                indicator.Update(marketData);
            }
        }
    }

    /// <inheritdoc />
    public void SetConfidenceThreshold(double threshold)
    {
        if (threshold < 0.0 || threshold > 1.0)
            throw new ArgumentOutOfRangeException(nameof(threshold), "Threshold must be between 0.0 and 1.0");

        _confidenceThreshold = threshold;
        _logger.LogInformation("Updated confidence threshold to {Threshold:F3}", threshold);
    }

    /// <summary>
    /// Sets the minimum indicator alignment requirement for signal generation
    /// </summary>
    public void SetIndicatorAlignmentThreshold(int threshold)
    {
        if (threshold < 2 || threshold > 9)
            throw new ArgumentOutOfRangeException(nameof(threshold), "Indicator alignment threshold must be between 2 and 9");

        var oldThreshold = _requiredIndicatorAlignment;
        _requiredIndicatorAlignment = threshold;
        _logger.LogInformation("🎯 Indicator alignment threshold updated from {OldThreshold} to {NewThreshold} (Adaptive System)",
            oldThreshold, threshold);
    }

    /// <inheritdoc />
    public SignalGenerationStats GetStatistics()
    {
        var upTime = DateTime.UtcNow - _startTime;
        var averageProcessingTime = _processingTimes.Count > 0 ? _processingTimes.Average() : 0.0;
        var averageConfidence = _highConfidenceSignals > 0 ? 
            _highConfidenceSignals / (double)_totalSignalsGenerated : 0.0;

        return new SignalGenerationStats
        {
            TotalSignalsGenerated = _totalSignalsGenerated,
            HighConfidenceSignals = _highConfidenceSignals,
            AverageConfidence = averageConfidence,
            AverageProcessingTimeMs = averageProcessingTime,
            LastSignalTime = DateTime.UtcNow,
            UpTime = upTime
        };
    }

    /// <summary>
    /// Configures user preferences for smart indicator selection
    /// </summary>
    public void SetUserPreferences(UserIndicatorPreferences preferences)
    {
        _userPreferences = preferences ?? throw new ArgumentNullException(nameof(preferences));
        _logger.LogInformation("🎯 Smart Selection preferences updated: Trend={TrendPref:F1}x, Volume={VolumePref:F1}x, OrderFlow={OrderFlowPref:F1}x, Mode={Mode}",
            preferences.TrendIndicatorPreference, preferences.VolumeIndicatorPreference,
            preferences.OrderFlowIndicatorPreference, preferences.SelectionMode);
    }

    /// <summary>
    /// CRITICAL FIX: Enhanced smart indicator selection with better integration
    /// </summary>
    public void SetSmartSelectionEnabled(bool enabled)
    {
        if (enabled && _selectionEngine == null)
        {
            _logger.LogWarning("⚠️ Smart Indicator Selection not available - selection engine not registered in DI");
            _logger.LogInformation("💡 To enable: Register ISmartIndicatorSelectionEngine in dependency injection");
            _logger.LogInformation("🔄 Falling back to traditional indicator selection");
            _useSmartSelection = false;
            return;
        }

        _useSmartSelection = enabled;

        if (enabled)
        {
            _logger.LogInformation("✅ Smart Indicator Selection ENABLED - Using adaptive indicator selection");
            _logger.LogInformation("🎯 Selection engine: {EngineType}", _selectionEngine?.GetType().Name ?? "Unknown");
        }
        else
        {
            _logger.LogInformation("🔧 Smart Indicator Selection DISABLED - Using all indicators");
        }
    }

    /// <summary>
    /// CRITICAL DEBUG: Gets threshold optimization suggestions for ultra-low timeframe demo data
    /// </summary>
    public Dictionary<string, ThresholdSuggestions> GetThresholdOptimizationSuggestions()
    {
        var suggestions = _thresholdOptimizer.GetOptimizationSuggestions();

        if (suggestions.Any())
        {
            _logger.LogInformation("🔧 THRESHOLD OPTIMIZATION SUGGESTIONS:");
            foreach (var kvp in suggestions)
            {
                var indicatorName = kvp.Key;
                var suggestion = kvp.Value;

                _logger.LogInformation($"   📊 {indicatorName}:");
                _logger.LogInformation($"      Signal Rate: {suggestion.CurrentSignalRate:P1} | Confidence: {suggestion.CurrentAvgConfidence:P1}");

                foreach (var recommendation in suggestion.Recommendations)
                {
                    _logger.LogInformation($"      💡 {recommendation}");
                }
            }
        }

        return suggestions;
    }

    private async Task<List<IndicatorReading>> UpdateIndicatorsAsync(MarketData marketData, CancellationToken cancellationToken)
    {
        var readings = new List<IndicatorReading>();

        _logger.LogDebug($"Updating {_indicators.Count} indicators for price: {marketData.Price:F8}");

        // Update indicators in parallel for better performance
        var tasks = _indicators.Where(i => i.IsEnabled).Select(async indicator =>
        {
            await Task.Run(() =>
            {
                try
                {
                    var reading = indicator.Update(marketData);
                    lock (readings)
                    {
                        readings.Add(reading);
                        _logger.LogDebug($"{indicator.Name}: Valid={reading.IsValid}, Direction={reading.Direction}, Confidence={reading.Confidence:P2}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating indicator {IndicatorName}", indicator.Name);
                    // Error already logged above with full exception details
                }
            }, cancellationToken);
        });

        await Task.WhenAll(tasks);

        _logger.LogDebug($"Indicator update complete: {readings.Count} readings generated");

        // CRITICAL DEBUG: Enhanced logging for Phase 1 indicator alignment debugging
        var validReadings = readings.Where(r => r.IsValid).ToList();
        var alignedReadings = readings.Where(r => r.IsValid && r.Direction != SignalDirection.None).ToList();

        _logger.LogInformation("📊 PHASE 1 INDICATOR PERFORMANCE SUMMARY:");
        _logger.LogInformation($"   📈 Total Indicators: {readings.Count}");
        _logger.LogInformation($"   ✅ Valid Readings: {validReadings.Count}");
        _logger.LogInformation($"   🎯 Aligned Readings: {alignedReadings.Count}");

        // Log each indicator's performance and analyze for optimization
        foreach (var reading in readings)
        {
            var status = reading.IsValid ? (reading.Direction != SignalDirection.None ? "🎯 ALIGNED" : "⚪ NEUTRAL") : "❌ INVALID";
            _logger.LogInformation($"   📊 {reading.IndicatorName}: {status} | Confidence: {reading.Confidence:P2} | Direction: {reading.Direction}");

            // CRITICAL DEBUG: Analyze indicator performance for threshold optimization
            if (reading.IsValid)
            {
                _thresholdOptimizer.AnalyzeIndicatorPerformance(reading.IndicatorName, reading);
            }
        }

        // Log specific issues for invalid indicators
        var invalidReadings = readings.Where(r => !r.IsValid).ToList();
        if (invalidReadings.Any())
        {
            _logger.LogWarning($"⚠️ {invalidReadings.Count} indicators are invalid:");
            foreach (var invalid in invalidReadings)
            {
                _logger.LogWarning($"   ❌ {invalid.IndicatorName}: Not ready or failed to generate reading");
            }
        }

        // Log neutral indicators (valid but no direction)
        var neutralReadings = validReadings.Where(r => r.Direction == SignalDirection.None).ToList();
        if (neutralReadings.Any())
        {
            _logger.LogInformation($"⚪ {neutralReadings.Count} indicators are neutral (no clear signal):");
            foreach (var neutral in neutralReadings)
            {
                _logger.LogInformation($"   ⚪ {neutral.IndicatorName}: Valid but no directional signal");
            }
        }

        return readings;
    }

    private int CountAlignedIndicators(List<IndicatorReading> readings)
    {
        var alignedCount = readings.Count(r => r.IsValid && r.Direction != SignalDirection.None);

        // CRITICAL DEBUG: Enhanced alignment analysis for ultra-low timeframe debugging
        _logger.LogInformation($"🎯 INDICATOR ALIGNMENT ANALYSIS:");
        _logger.LogInformation($"   📊 Aligned Indicators: {alignedCount}/{readings.Count}");
        _logger.LogInformation($"   🎯 Required Alignment: {_requiredIndicatorAlignment}");
        _logger.LogInformation($"   ✅ Alignment Status: {(alignedCount >= _requiredIndicatorAlignment ? "PASSED" : "FAILED")}");

        if (alignedCount > 0)
        {
            var alignedIndicators = readings.Where(r => r.IsValid && r.Direction != SignalDirection.None).ToList();
            _logger.LogInformation($"   🎯 Aligned Indicators List:");
            foreach (var aligned in alignedIndicators)
            {
                _logger.LogInformation($"      ✅ {aligned.IndicatorName}: {aligned.Direction} ({aligned.Confidence:P2})");
            }
        }
        else
        {
            _logger.LogWarning($"   ⚠️ NO INDICATORS ALIGNED - Investigating reasons:");
            foreach (var reading in readings)
            {
                if (!reading.IsValid)
                {
                    _logger.LogWarning($"      ❌ {reading.IndicatorName}: INVALID (not ready or error)");
                }
                else if (reading.Direction == SignalDirection.None)
                {
                    _logger.LogWarning($"      ⚪ {reading.IndicatorName}: NEUTRAL (valid but no signal)");
                }
            }
        }

        return alignedCount;
    }

    private double CalculateWeightedConfidence(List<IndicatorReading> readings)
    {
        var totalWeight = 0.0;
        var weightedConfidence = 0.0;

        _logger.LogDebug("Calculating weighted confidence from {TotalReadings} readings ({ValidReadings} valid, {AlignedReadings} aligned)",
            readings.Count, readings.Count(r => r.IsValid), readings.Count(r => r.IsValid && r.Direction != SignalDirection.None));

        foreach (var reading in readings.Where(r => r.IsValid && r.Direction != SignalDirection.None))
        {
            if (_indicatorWeights.TryGetValue(reading.IndicatorName, out var weight))
            {
                _logger.LogDebug("{IndicatorName}: Confidence={Confidence:P2}, Weight={Weight:P2}, Direction={Direction}",
                    reading.IndicatorName, reading.Confidence, weight, reading.Direction);
                weightedConfidence += reading.Confidence * weight;
                totalWeight += weight;
            }
            else
            {
                _logger.LogWarning("Indicator {IndicatorName} not found in weights dictionary", reading.IndicatorName);
            }
        }

        var finalConfidence = totalWeight > 0 ? weightedConfidence / totalWeight : 0.0;
        _logger.LogDebug("Final confidence calculation: {WeightedConfidence:F4} / {TotalWeight:F4} = {FinalConfidence:P2}",
            weightedConfidence, totalWeight, finalConfidence);

        // Production confidence calculation: Use weighted average of indicator confidences
        // No fallback logic needed - indicators provide real confidence values
        return finalConfidence;
    }

    private SignalDirection DetermineSignalDirection(List<IndicatorReading> readings)
    {
        var buyWeight = 0.0;
        var sellWeight = 0.0;

        foreach (var reading in readings.Where(r => r.IsValid && r.Direction != SignalDirection.None))
        {
            if (_indicatorWeights.TryGetValue(reading.IndicatorName, out var weight))
            {
                var adjustedWeight = weight * reading.Confidence;
                
                if (reading.Direction == SignalDirection.Buy)
                    buyWeight += adjustedWeight;
                else if (reading.Direction == SignalDirection.Sell)
                    sellWeight += adjustedWeight;
            }
        }

        if (buyWeight > sellWeight * 1.2) // Require 20% margin for clear direction
            return SignalDirection.Buy;
        else if (sellWeight > buyWeight * 1.2)
            return SignalDirection.Sell;
        else
            return SignalDirection.None;
    }

    private TradingSignal CreateHighConfidenceSignal(
        MarketData marketData, 
        List<IndicatorReading> readings, 
        SignalDirection direction, 
        double confidence, 
        int alignedCount)
    {
        var entryPrice = (marketData.Bid + marketData.Ask) / 2;
        var targetMove = 0.005m; // 0.5% target move
        
        var takeProfit = direction == SignalDirection.Buy 
            ? entryPrice * (1 + targetMove)
            : entryPrice * (1 - targetMove);

        var stopLoss = direction == SignalDirection.Buy
            ? entryPrice * (1 - targetMove / 2) // 0.25% stop loss
            : entryPrice * (1 + targetMove / 2);

        var indicatorContributions = readings
            .Where(r => r.IsValid && r.Direction != SignalDirection.None)
            .Select(r => new IndicatorContribution
            {
                IndicatorName = r.IndicatorName,
                Confidence = r.Confidence,
                Weight = _indicatorWeights.GetValueOrDefault(r.IndicatorName, 0.0),
                Values = r.AdditionalValues.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value),
                Timestamp = r.Timestamp
            })
            .ToList();

        return new TradingSignal
        {
            Symbol = marketData.Symbol,
            Direction = direction,
            Confidence = confidence,
            EntryPrice = entryPrice,
            TakeProfit = takeProfit,
            StopLoss = stopLoss,
            PositionSize = 1, // Will be determined by position sizing logic
            Priority = (int)(confidence * 100),
            ExpectedDuration = TimeSpan.FromMinutes(5), // Expected duration for 0.5% move
            RiskRewardRatio = 2.0, // 0.5% profit vs 0.25% loss
            SourceData = marketData,
            IndicatorContributions = indicatorContributions,
            Metadata = new Dictionary<string, object>
            {
                ["IndicatorsAligned"] = alignedCount,
                ["RequiredAlignment"] = _requiredIndicatorAlignment,
                ["ConfidenceThreshold"] = _confidenceThreshold,
                ["TargetMove"] = targetMove,
                ["GenerationMethod"] = "HighProbabilitySignalGenerator"
            }
        };
    }

    private TradingSignal CreateNoSignal(MarketData marketData, List<IndicatorReading> readings, string reason)
    {
        _totalSignalsGenerated++;
        TrackProcessingTime(DateTime.UtcNow);

        return new TradingSignal
        {
            Symbol = marketData.Symbol,
            Direction = SignalDirection.None,
            Confidence = 0.0,
            EntryPrice = (marketData.Bid + marketData.Ask) / 2,
            SourceData = marketData,
            Metadata = new Dictionary<string, object>
            {
                ["Reason"] = reason,
                ["IndicatorsReady"] = readings.Count(r => r.IsValid),
                ["IndicatorsAligned"] = CountAlignedIndicators(readings),
                ["RequiredAlignment"] = _requiredIndicatorAlignment,
                ["ConfidenceThreshold"] = _confidenceThreshold
            }
        };
    }

    private TradingSignal CreateErrorSignal(MarketData marketData, string errorMessage)
    {
        return new TradingSignal
        {
            Symbol = marketData.Symbol,
            Direction = SignalDirection.None,
            Confidence = 0.0,
            EntryPrice = 0,
            SourceData = marketData,
            Metadata = new Dictionary<string, object>
            {
                ["Error"] = errorMessage,
                ["GenerationMethod"] = "HighProbabilitySignalGenerator"
            }
        };
    }

    private void TrackProcessingTime(DateTime startTime)
    {
        var processingTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
        _processingTimes.Add(processingTime);

        // Keep only last 1000 measurements
        if (_processingTimes.Count > 1000)
        {
            _processingTimes.RemoveAt(0);
        }

        // Fire statistics update every 100 signals
        if (_totalSignalsGenerated % 100 == 0)
        {
            StatisticsUpdated?.Invoke(this, GetStatistics());
        }
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var indicator in _indicators.OfType<IDisposable>())
            {
                indicator.Dispose();
            }
            _disposed = true;
        }
    }
}
