<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SmartVolumeStrategy.ATAS</name>
    </assembly>
    <members>
        <member name="T:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions">
            <summary>
            Extension methods for configuring ATAS-specific services
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddATASServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds ATAS platform services to the service collection
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddATASPlatformServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds ATAS platform abstraction services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddPerformanceOptimization(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds performance optimization services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddStrategyServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds strategy-specific services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddPhase2BiasCorrection(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds Phase 2 Bias Correction services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.ValidateATASServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Validates that all required services are properly registered
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddPhase2PressureDetection(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds Phase 2 Enhanced Market Pressure Detection services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddSignalCoordination(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds Signal Coordination System services
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddPhase1EnhancedIndicators(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds Phase 1 Enhanced Indicator services for multi-timeframe and volume-weighted analysis
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.ATASServiceExtensions.AddNewMarketAdaptiveArchitecture(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            PERMANENT ARCHITECTURAL OVERHAUL: Adds new market-adaptive signal generation services
            This replaces the over-engineered complex system with simplified, market-adaptive approach
            </summary>
            <param name="services">Service collection</param>
            <returns>Service collection for chaining</returns>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.DependencyInjection.IndicatorCalculationPoolPolicy">
            <summary>
            Object pool policy for IndicatorCalculation objects
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.DependencyInjection.IStrategyFactory">
            <summary>
            Strategy factory interface
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.DependencyInjection.IStrategyFactory.CreateStrategy">
            <summary>
            Creates a new strategy instance
            </summary>
            <returns>Strategy instance</returns>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.DependencyInjection.StrategyFactory">
            <summary>
            Strategy factory implementation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.DependencyInjection.MockTradingManager">
            <summary>
            Mock trading manager for development and testing
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.DependencyInjection.MockInstrumentInfo">
            <summary>
            Mock instrument info for development and testing
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy">
            <summary>
            High-Probability Scalping Strategy for ATAS Platform
            Implements sophisticated signal generation with position sizing and risk management
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.MaxPositions">
            <summary>
            Maximum number of open positions allowed simultaneously.
            Hardcoded to 1 for single-position trading strategy.
            This eliminates UI configuration issues and enforces the strategy's design.
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.#ctor">
            <summary>
            Initializes the High-Probability Scalping Strategy
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializeEnhancedPressureDetection">
            <summary>
            Initializes the Enhanced Pressure Detection Engine
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializeCoreIntegration">
            <summary>
            Initializes the Core integration with dependency injection and signal generator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ConfigureCoreServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures Core services for dependency injection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.RegisterAdaptiveSystemComponents(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers Core Adaptive System components for dependency injection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.RegisterPhase2PressureDetectors(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers Phase 2 Enhanced Market Pressure Detection detectors for dependency injection
            CRITICAL FIX: Proper logger factory registration to resolve DI initialization failure
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ConfigureSmartIndicatorSelection">
            <summary>
            Configures Smart Indicator Selection based on user preferences
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.RegisterSignalCoordinationSystem(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers Signal Coordination System for dependency injection
            CRITICAL FIX: This was missing and causing the main integration failure
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializeAdaptiveSystem">
            <summary>
            Initializes the Core Adaptive System components
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializeNewArchitecture">
            <summary>
            PERMANENT ARCHITECTURAL OVERHAUL: Initializes the new market-adaptive signal generation system
            This replaces the over-engineered complex system with a simplified, market-adaptive approach
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializePhase1EnhancedIndicators">
            <summary>
            Initializes Phase 1 Enhanced Indicators for multi-timeframe and volume-weighted analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializePhase2BiasCorrection">
            <summary>
            Initializes Phase 2 Bias Correction system for systematic bias corrections
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ConvertToMarketData(System.Int32,System.Decimal)">
            <summary>
            Converts ATAS market data to Core MarketData format using real order flow data
            OPTIMIZED: Reduced processing time from 5-12ms to less than 2ms target
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetRealVolumeDataOptimized(System.Int32,System.Decimal)">
            <summary>
            Gets real volume data from ATAS for the current bar with optimized performance
            OPTIMIZED: Improved bounds checking and caching to reduce processing time
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetMarketDepthVolumesOptimized">
            <summary>
            Gets bid/ask volumes from ATAS market depth data with optimized performance
            OPTIMIZED: Improved caching and reduced processing time
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetMarketDepthVolumes">
            <summary>
            Gets bid/ask volumes from ATAS market depth data
            ENHANCED: Uses cached real-time data from OnBestBidAskChanged callback
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EstimateVolumeFromPriceAction(System.Decimal)">
            <summary>
            Estimates volume from price action when real volume data is unavailable
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EstimateRealisticVolumeForDemo(System.Decimal)">
            <summary>
            Enhanced volume estimation specifically designed for demo data compatibility
            Provides more realistic volume patterns that work with indicator calculations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsRunningOnDemoData">
            <summary>
            Detects if the strategy is running on demo/test data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.AdjustMarketDataForDemo(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Adjusts market data for demo environment compatibility
            Ensures indicators receive valid data that works with their calculations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.DetermineTradeDirection(System.Decimal)">
            <summary>
            Determines trade direction from recent price movement
            ENHANCED: Uses price momentum analysis for direction detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnHighConfidenceSignalGenerated(System.Object,SmartVolumeStrategy.Core.Models.TradingSignal)">
            <summary>
            Event handler for high-confidence signals from the Core signal generator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnSignalStatisticsUpdated(System.Object,SmartVolumeStrategy.Core.Interfaces.SignalGenerationStats)">
            <summary>
            Event handler for signal generation statistics updates
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnAdaptiveSystemAdapted(System.Object,SmartVolumeStrategy.Core.Adaptive.AdaptationEvent)">
            <summary>
            Event handler for adaptive system parameter adaptations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnAdaptiveSystemStatusChanged(System.Object,SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus)">
            <summary>
            Event handler for adaptive system status changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ApplyParameterAdjustment(SmartVolumeStrategy.Core.Models.ParameterAdjustment)">
            <summary>
            Applies parameter adjustments from the adaptive system to the strategy
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ApplyVolumeThresholdAdjustment(System.Double,System.String)">
            <summary>
            Applies volume threshold multiplier adjustments to volume-based indicators
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ApplyVWAPDeviationAdjustment(System.Double,System.String)">
            <summary>
            Applies VWAP deviation threshold adjustments to VWAP-based indicators
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PerformPhase3MonitoringAsync">
            <summary>
            PHASE 3: Performs comprehensive adaptive trigger monitoring and validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetCurrentMarketDataForPhase3">
            <summary>
            PHASE 3: Gets current market data for Phase 3 analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.UpdatePhase3Status(SmartVolumeStrategy.Core.Models.Phase3AnalysisReport)">
            <summary>
            PHASE 3: Updates strategy status with Phase 3 analysis results
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnStarted">
            <summary>
            CRITICAL: ATAS Strategy Started Handler - Required for proper property initialization
            This method is called by ATAS when the strategy starts and user properties are loaded
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ConfigureIndicatorAlignmentThreshold">
            <summary>
            Configures the indicator alignment threshold after user configuration is loaded
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateUserConfiguration">
            <summary>
            Validates user-configured parameters to ensure they're within acceptable ranges
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnCalculate(System.Int32,System.Decimal)">
            <summary>
            Main calculation method called by ATAS for each bar
            This is the core method that must be implemented for ATAS strategies
            </summary>
            <param name="bar">Current bar index</param>
            <param name="value">Current bar value</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnNewTrade(System.Object)">
            <summary>
            Called for each new trade - feeds order flow analyzers
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnBestBidAskChanged(System.Object)">
            <summary>
            Called when best bid/ask changes - feeds order book imbalance detector
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ProcessOrderFlowData(SmartVolumeStrategy.ATAS.Platform.OrderFlowData)">
            <summary>
            Processes order flow data with the enhanced signal generator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ProcessOrderBookUpdate(System.Decimal,System.Int64,System.Boolean,System.DateTime)">
            <summary>
            Processes order book updates for imbalance detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetTradePrice(System.Object)">
            <summary>
            Extracts trade price from trade object (compatibility layer)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetTradeVolume(System.Object)">
            <summary>
            Extracts trade volume from trade object (compatibility layer)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetTradeDirection(System.Object)">
            <summary>
            Extracts trade direction from trade object (compatibility layer)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetTradeAggressiveness(System.Object)">
            <summary>
            Determines if trade is aggressive (market order vs limit order)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetDepthPrice(System.Object)">
            <summary>
            Extracts depth price from depth object (compatibility layer)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetDepthVolume(System.Object)">
            <summary>
            Extracts depth volume from depth object (compatibility layer)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetDepthSide(System.Object)">
            <summary>
            Determines if depth update is for bid side
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ConvertToOrderFlowDirection(SmartVolumeStrategy.Core.Models.SignalDirection)">
            <summary>
            Converts SignalDirection to order flow TradeDirection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EstimateDepthVolume(System.Boolean)">
            <summary>
            Estimates depth volume for bid or ask side
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ShouldCheckTickSignals">
            <summary>
            Determines if tick-based signals should be checked
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ShouldCheckPressureSignals">
            <summary>
            Determines if pressure-based signals should be checked
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckPressureBasedSignals(System.Decimal)">
            <summary>
            ENHANCED: Checks for buy/sell pressure-based signals using Enhanced Pressure Detection Engine
            Implements Phase 1 and Phase 2 pressure detection with less than 20ms performance target
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.MonitorPerformance(System.String,System.Double)">
            <summary>
            ENHANCED: Performance monitoring for order flow processing
            Tracks processing times to ensure less than 20ms requirement is met
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ReportPerformanceMetrics">
            <summary>
            Reports comprehensive performance metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializeOrderFlowTracking">
            <summary>
            Initializes order flow data tracking collections
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CreateMarketDataForPressureAnalysis(System.Decimal)">
            <summary>
            Creates market data for pressure analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckOrderBookPressure(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Checks order book pressure for buy/sell signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckVolumeDeltaPressure(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Checks volume delta pressure for directional bias
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckAggressiveTradingPressure(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Checks for aggressive trading patterns indicating strong pressure
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CombinePressureSignals(SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PressureSignal,SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PressureSignal,SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PressureSignal)">
            <summary>
            Combines multiple pressure signals into a single high-confidence signal
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PressureSignal">
            <summary>
            Pressure signal data structure
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.LogLevel">
            <summary>
            Log levels for different types of messages
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.InitializeLoggingSystem">
            <summary>
            Initializes the comprehensive logging system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.TestLoggingMechanisms">
            <summary>
            Tests all available logging mechanisms to verify they work
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.LogMessage(System.String,SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.LogLevel)">
            <summary>
            Enhanced logging method with multiple output destinations and log levels
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetRecentLogMessages(System.Int32)">
            <summary>
            Gets recent log messages for debugging
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ProcessCoreSignalGeneration(System.Int32,System.Decimal)">
            <summary>
            Processes signal generation using the Core indicator system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ProcessNewArchitectureSignalGeneration(SmartVolumeStrategy.Core.Models.MarketData,System.Int32,System.Decimal)">
            <summary>
            PERMANENT ARCHITECTURAL OVERHAUL: Processes signal generation using the new market-adaptive system
            This replaces the over-engineered complex system with simplified, market-adaptive approach
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.UpdatePhase1EnhancedIndicators(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates Phase 1 Enhanced Indicators with current market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EnhanceSignalWithMultiTimeframe(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.MultiTimeframeSignal)">
            <summary>
            Enhances a trading signal with multi-timeframe confluence analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ProcessFallbackSignalGeneration(System.Int32,System.Decimal)">
            <summary>
            Fallback signal generation when Core integration is not available
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckSignalGenerationHealth">
            <summary>
            CRITICAL FIX: Monitor signal generation health to detect "turning off" issues
            Automatically restarts signal generation if it stops working
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculateSignalStrength(System.Int32,System.Decimal)">
            <summary>
            Calculates signal strength based on simplified indicators
            Implements a basic high-probability system for ATAS visibility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculatePriceMomentum(System.Decimal)">
            <summary>
            REAL INDICATOR: Calculates price momentum based on recent price movements
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculateVolumeStrength(System.Decimal)">
            <summary>
            REAL INDICATOR: Calculates volume strength based on volume patterns
            CRITICAL FIX: Eliminated downward bias in volume calculation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculateMarketTimingFactor">
            <summary>
            REAL INDICATOR: Calculates market timing factor based on trading hours and day
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculateVolatilityFactor(System.Decimal)">
            <summary>
            REAL INDICATOR: Calculates volatility factor based on price stability
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculateConfidence(System.Int32,System.Decimal)">
            <summary>
            CRITICAL FIX: Calculates confidence level using REAL indicator analysis (no random logic)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CanGenerateSignal">
            <summary>
            Checks if a new signal can be generated (rate limiting)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.LogDiagnosticInfo(System.Int32,System.Decimal,System.DateTime)">
            <summary>
            Logs comprehensive diagnostic information about strategy state
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GenerateTradingSignal(System.Int32,System.Decimal,System.Double,System.Double)">
            <summary>
            Generates a trading signal when conditions are met
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ProcessAdaptiveSystemUpdate(System.Int32,System.Decimal)">
            <summary>
            Processes market data through the Core Adaptive System for regime detection and parameter adaptation
            CRITICAL FIX: Optimized for performance to prevent degradation over time
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.UpdateAdaptivePerformanceTracking">
            <summary>
            Updates adaptive performance tracking with real trade results
            PRIORITY 3A: Enhanced with detailed tracking and trigger monitoring
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ConvertToAdaptiveTradeResult(SmartVolumeStrategy.Core.Models.TradeResult)">
            <summary>
            Converts Models.TradeResult to Adaptive.TradeResult for adaptive system compatibility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.MonitorAdaptiveSystemStatus">
            <summary>
            PRIORITY 3B: Monitors adaptive system status and triggers
            Provides detailed feedback on adaptation conditions and system health
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckAdaptationTriggerConditions(SmartVolumeStrategy.Core.Models.AdaptiveSystemStatus)">
            <summary>
            PRIORITY 3B: Checks conditions that might trigger adaptations
            Provides early warning for potential system adaptations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsUsingDefaultStopLoss">
            <summary>
            CRITICAL FIX: Improved default value detection for Stop Loss
            Tracks configuration state more reliably than hard-coded value comparison
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsUsingDefaultTakeProfit">
            <summary>
            FIXED: Improved default value detection for Take Profit
            Eliminates false positive warnings when user intentionally sets 0.5%
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateConfiguration">
            <summary>
            CRITICAL FIX: Validates configuration parameters to prevent edge cases
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GenerateOrderComment(System.String,System.String)">
            <summary>
            CRITICAL FIX: Generates standardized order comments for consistent tracking
            Format: HPSS-{TYPE}-{TIMESTAMP} for proper order identification and cleanup
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ParseOrderComment(System.String)">
            <summary>
            CRITICAL FIX: Extracts order type and timestamp from standardized comment
            Used for order tracking and cleanup operations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetActualOpenPositionsCount">
            <summary>
            CRITICAL FIX: Gets the actual number of open positions from ATAS
            This replaces manual position counting with real position data to prevent multiple positions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ForceEnableCoreIndicators">
            <summary>
            CRITICAL FIX: Force enable core indicators and diagnostic logging
            Use this to troubleshoot why Phase 1 indicators are not working
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ForcePositionCounterReset(System.String)">
            <summary>
            CRITICAL FIX: Manual position counter reset for synchronization bugs
            Use this when ATAS shows no positions but strategy thinks positions are open
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CleanupStaleTPSLOrders">
            <summary>
            Cleans up stale TP/SL orders that are no longer active
            CRITICAL FIX: Removes completed/cancelled orders from tracking to prevent position estimation errors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EstimatePositionsFromOrders">
            <summary>
            Estimates open positions by analyzing order states
            This is a fallback method when direct position access is not available
            ENHANCED: Now validates order states and cleans up stale orders
            CRITICAL FIX: Added position counter synchronization to prevent tracking bugs
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ForcePositionSynchronization(System.String)">
            <summary>
            CRITICAL FIX: Thread-safe position synchronization with ATAS reality
            This method should be called periodically to ensure position tracking accuracy
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsInTradeCooldown">
            <summary>
            CRITICAL FIX: Check if strategy is in trade cooldown period
            This prevents rapid re-entry after position closure
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsInOrderCooldown">
            <summary>
            CRITICAL FIX: Check if strategy is in order cooldown period
            This prevents rapid order placement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.HasRecentOrderActivity">
            <summary>
            CRITICAL FIX: Check if recent order activity should block new trades
            This includes pending orders and recent order placement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CanPlaceNewPosition">
            <summary>
            PRIORITY 2 FIX: Atomic position checking with race condition prevention
            Checks if a new position can be placed and RESERVES the position slot immediately
            ENHANCED: Uses actual ATAS position data instead of manual counters to prevent multiple positions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ReleasePositionLock(System.String)">
            <summary>
            PRIORITY 2 FIX: Releases the position opening lock after position confirmation
            This must be called after position is confirmed to allow new positions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidatePositionState">
            <summary>
            PRIORITY 2 FIX: Validates current position state for consistency
            Detects duplicate TP/SL orders and position tracking issues
            ENHANCED: Now detects position accumulation violations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EmergencyPositionReset(System.String)">
            <summary>
            PRIORITY 2 FIX: Emergency position reset for critical state recovery
            Resets all position tracking and locks when state becomes inconsistent
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetActualPositionSize">
            <summary>
            EMERGENCY FIX: Gets actual position size in USDT to detect accumulation
            Uses the same approach as GetActualOpenPositionsCount() for consistency
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EmergencyPositionClosureAsync(System.String)">
            <summary>
            EMERGENCY FIX: Emergency position closure for position accumulation violations
            Uses conservative approach - triggers emergency reset instead of trying to close positions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculatePositionSize(System.Decimal,System.Double)">
            <summary>
            Calculates position size based on USDT amount and current market price
            Implements proper tick size rounding and risk management
            </summary>
            <param name="currentPrice">Current market price</param>
            <param name="confidence">Signal confidence (0.0 to 1.0)</param>
            <returns>Position size in contracts/shares</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.RoundToValidQuantity(System.Decimal)">
            <summary>
            Rounds quantity to comply with instrument's minimum tick size
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ApplyPositionLimits(System.Decimal,System.Decimal)">
            <summary>
            Applies position size limits and validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ExecuteTrade(System.String,System.Decimal,System.Decimal,System.Double)">
            <summary>
            Executes a trade with the calculated position size
            Enhanced with proper position tracking and realistic price handling
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckForATASBuiltInTPSL(ATAS.DataFeedsCore.Order)">
            <summary>
            CRITICAL FIX: Checks if ATAS has actually attached built-in TP/SL to an order
            This prevents the mismatch between configuration and actual order behavior
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.StartBuiltInTPSLMonitoring(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            CRITICAL FIX: Starts monitoring for built-in TP/SL position closure
            This ensures positions are properly tracked even with ATAS built-in TP/SL
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.SimulatePositionClosure(System.String,System.Decimal,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Simulates position closure for testing purposes
            In real trading, this would be handled by actual ATAS orders
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculateStopLoss(System.Decimal,System.String)">
            <summary>
            Calculates stop loss price based on direction and risk parameters
            Enhanced to handle low prices and provide meaningful differences
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CalculateTakeProfit(System.Decimal,System.String)">
            <summary>
            Calculates take profit price based on direction and target parameters
            Enhanced to handle low prices and provide meaningful differences
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.LogTradeExecution(System.String,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Double)">
            <summary>
            Logs detailed trade execution information
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateATASTradingEnvironment">
            <summary>
            Validates ATAS trading environment before placing orders
            Checks connection, permissions, account balance, and instrument setup
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.DetectTradingEnvironment">
            <summary>
            Detects if running in demo/test environment vs live trading
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsATASTradingEnabled">
            <summary>
            Checks if ATAS trading is enabled and functional
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateAccountAndPositionLimits(System.String,System.Decimal,System.Decimal)">
            <summary>
            Validates account balance and position limits before placing orders
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.RoundQuantityToExchangeRequirements(System.Decimal)">
            <summary>
            Rounds order quantity to comply with exchange minimum volume step requirements
            Prevents "Can't place order" popups due to volume precision violations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetInstrumentVolumeStep">
            <summary>
            Gets the minimum volume step for the current instrument
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.StartCallbackMonitoring(ATAS.DataFeedsCore.Order,System.String,System.Double)">
            <summary>
            Starts monitoring for order callbacks to detect if they're not being triggered
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetActualFillPrice(ATAS.DataFeedsCore.Order)">
            <summary>
            Gets the actual fill price from a filled order
            CRITICAL FIX: Use current market price for market orders
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetCurrentMarketPrice">
            <summary>
            Gets the current market price from the most recent bar data
            FIXED: Use _lastValidPrice which is updated in ValidateMarketData
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.HandleTPSLOrderUpdate(ATAS.DataFeedsCore.Order)">
            <summary>
            Handle TP/SL order state changes with automatic cleanup (based on working VolumeBlockStrategy pattern)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CancelRemainingTPSLOrders(System.String)">
            <summary>
            Cancel remaining TP/SL orders when one executes (based on working VolumeBlockStrategy pattern)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.AddOrderToTracking(ATAS.DataFeedsCore.Order,System.String,System.String)">
            <summary>
            PRIORITY 3 FIX: Add order to tracking system with comprehensive diagnostics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.RemoveOrderFromTracking(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            PRIORITY 3 FIX: Remove order from tracking system with comprehensive cleanup
            ENHANCED: Now handles position lock reset for main orders that timeout
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsOrderTracked(ATAS.DataFeedsCore.Order,System.String@)">
            <summary>
            PRIORITY 3 FIX: Check if order is properly tracked with recovery attempt
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateTrackingSystemHealth">
            <summary>
            PRIORITY 3 FIX: Validate tracking system health
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.IsDemoTradingMode">
            <summary>
            PRIORITY 4 FIX: Detect if we're running in demo/paper trading mode
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PlaceATASOrder(System.String,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Double)">
            <summary>
            Places an actual ATAS order using the platform's order management system
            Enhanced with comprehensive validation and debugging
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PlaceTPSLOrdersAfterFill(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            PRIORITY 2 FIX: Places Take Profit and Stop Loss orders after main order fills
            ENHANCED: Implements TP/SL placement locking to prevent duplicate order creation
            This is the CRITICAL missing piece that implements actual TP/SL risk management
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PlaceStopLossOrder(ATAS.DataFeedsCore.OrderDirections,System.Decimal,System.Decimal,System.String)">
            <summary>
            Places a Stop Loss order to limit downside risk
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateStopLossPrice(ATAS.DataFeedsCore.OrderDirections,System.Decimal,System.String)">
            <summary>
            Validates that the stop loss price is placed correctly to avoid immediate execution
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PlaceTakeProfitOrder(ATAS.DataFeedsCore.OrderDirections,System.Decimal,System.Decimal,System.String)">
            <summary>
            Places a Take Profit order to secure gains
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CleanupAllRelatedTPSLOrdersAsync(ATAS.DataFeedsCore.Order)">
            <summary>
            DEPRECATED: This method has been replaced by CleanupRelatedOrdersAfterTPSLFillAsync
            which provides more sophisticated order matching and cleanup logic
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CleanupRelatedOrdersAfterTPSLFillAsync(System.String)">
            <summary>
            Cleans up all related orders when a TP/SL order fills
            SIMPLIFIED: Uses direct comment-based matching instead of complex order ID extraction
            This prevents orphaned orders and ensures proper risk management
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CancelRelatedOrderAsync(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            Cancels a related order using bulletproof ATAS API with proper error handling
            ENHANCED: Uses CancelOrderAsync with state validation and confirmation tracking
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ResetPositionTracking(System.String)">
            <summary>
            CRITICAL FIX: Safe position tracking reset that validates actual positions
            This method now checks actual ATAS positions before resetting counters
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.RestartSignalGenerationSystem(System.String)">
            <summary>
            CRITICAL FIX: Restart signal generation system to prevent "turning off" after cleanup
            This ensures the strategy continues generating signals after emergency cleanup events
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ForcePositionTrackingReset">
            <summary>
            EMERGENCY FIX: Force position tracking reset when positions are confirmed closed
            Use this when you manually confirm all positions are closed but strategy is still blocked
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PerformStartupOrphanedOrderCleanupAsync">
            <summary>
            Startup cleanup method to detect and cancel orphaned TP/SL orders from previous sessions
            This runs when the strategy starts to clean up any orders left behind from crashes or restarts
            ENHANCED: Now detects and fixes position accumulation violations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PerformEmergencyPositionCleanupAsync(System.String)">
            <summary>
            Emergency position cleanup method for unprotected positions
            Closes positions that have lost their TP/SL protection due to order cancellations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PerformFallbackTPSLCleanupAsync(System.String)">
            <summary>
            Emergency fallback cleanup method that cancels any remaining TP/SL orders
            ENHANCED: Handles both tracked and untracked orders with comprehensive detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnOrderRegisterFailed(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            Called when an order fails to be placed on the exchange
            Enhanced with comprehensive error reporting and ATAS notifications
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnOrderChanged(ATAS.DataFeedsCore.Order)">
            <summary>
            Called when an order status changes (placed, filled, canceled, etc.)
            ENHANCED: Now properly handles position counting and comprehensive debugging
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnOrderCancelFailed(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            Called when an order cancellation fails
            CRITICAL: This callback is essential for bulletproof order cleanup
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateMarketData(System.Int32,System.Decimal)">
            <summary>
            Validates incoming market data and logs warnings for suspicious data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckForStuckPositionLocks">
            <summary>
            CRITICAL FIX: Check for stuck position locks and automatically reset them
            This prevents the strategy from getting permanently stuck after order timeouts
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckForCallbackTimeouts">
            <summary>
            EMERGENCY FIX: Enhanced callback timeout system with aggressive demo mode protection
            Checks for orders that haven't received callbacks within expected timeframe
            Implements emergency position closure for unprotected positions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EmergencyPositionProtectionAsync(System.String)">
            <summary>
            EMERGENCY FIX: Emergency position protection for unprotected positions
            Implements aggressive safety measures when TP/SL orders fail
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.EmergencyCloseAllPositionsAsync(System.String)">
            <summary>
            EMERGENCY FIX: Emergency closure of all open positions
            Used when positions are detected without proper TP/SL protection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.CheckForUnprotectedPositions">
            <summary>
            EMERGENCY FIX: Periodic check for unprotected positions
            Monitors for positions without proper TP/SL protection and triggers emergency measures
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.LogPositionTrackingDiagnostics">
            <summary>
            Logs detailed position tracking diagnostics for debugging
            ENHANCED: Now includes position tracking integrity validation and automatic recovery
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ValidateAndRecoverPositionTracking">
            <summary>
            Validates position tracking integrity and performs automatic recovery if needed
            CRITICAL: This method ensures the strategy can continue trading after any tracking issues
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.GetStrategyDiagnostics">
            <summary>
            Gets comprehensive strategy diagnostics for troubleshooting
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.OnDispose">
            <summary>
            Disposes of the strategy and cleans up resources
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.ShouldRecreateOrder(System.String)">
            <summary>
            CRITICAL FIX: Check if an order should be recreated based on attempt limits
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.AttemptTPSLOrderRecreation(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            CRITICAL FIX: Attempt to recreate a canceled TP/SL order
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.HighProbabilityScalpingStrategy.PerformEmergencyCleanupAfterRecreationFailure(System.String,System.Int32)">
            <summary>
            CRITICAL FIX: Perform emergency cleanup after order recreation fails
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator">
            <summary>
            Phase 3 Market Data Coordinator - Integrates all enhanced market data features
            Coordinates market depth, volume profiles, and order flow analysis
            Target: under 20ms total processing time for all Phase 3 features
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.EnhancedDataAvailable">
            <summary>
            Event fired when enhanced market data is available
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.MarketStructureChanged">
            <summary>
            Event fired when significant market structure change detected
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator},SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor,SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer,SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer,System.Decimal,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Initializes the Phase 3 market data coordinator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.ProcessMarketUpdate(SmartVolumeStrategy.Core.Models.MarketData,System.Nullable{System.Decimal},System.Nullable{System.Int64},System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
            <summary>
            Processes comprehensive market data update
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.CalculateLiquidityAnalysis(SmartVolumeStrategy.ATAS.Models.MarketDepth,SmartVolumeStrategy.ATAS.Models.VolumeProfile,SmartVolumeStrategy.ATAS.Models.OrderFlowData)">
            <summary>
            Calculates comprehensive liquidity analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.CalculateLiquidityScore(SmartVolumeStrategy.ATAS.Models.MarketDepth,SmartVolumeStrategy.ATAS.Models.VolumeProfile)">
            <summary>
            Calculates liquidity score (0-100)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.DetectLiquidityGaps(SmartVolumeStrategy.ATAS.Models.MarketDepth)">
            <summary>
            Detects liquidity gaps in order book
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.EstimateMarketImpact(SmartVolumeStrategy.ATAS.Models.MarketDepth,SmartVolumeStrategy.ATAS.Models.OrderFlowData)">
            <summary>
            Estimates market impact for standard trade size
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.EstimateFillTime(SmartVolumeStrategy.ATAS.Models.MarketDepth)">
            <summary>
            Estimates fill time for standard order
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.AnalyzeMarketStructure(SmartVolumeStrategy.ATAS.Models.EnhancedMarketData)">
            <summary>
            Analyzes for significant market structure changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.SubscribeToEvents">
            <summary>
            Subscribes to component events
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.GetPerformanceStats">
            <summary>
            Gets comprehensive performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.TrackPerformance(System.Int64,System.Int64)">
            <summary>
            Tracks processing performance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Integration.Phase3MarketDataCoordinator.Dispose">
            <summary>
            Disposes the coordinator
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor">
            <summary>
            High-performance market depth processor for Phase 3
            Processes real-time order book updates with top 5 level analysis
            Target: under 5ms processing time for depth updates
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.DepthChanged">
            <summary>
            Event fired when significant depth change detected
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.ImbalanceDetected">
            <summary>
            Event fired when order book imbalance detected
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor})">
            <summary>
            Initializes the market depth processor
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.ProcessDepthUpdate(System.Decimal,System.Int64,System.Boolean,System.DateTime)">
            <summary>
            Processes market depth update with high-performance optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.UpdateBidLevel(System.Decimal,System.Int64)">
            <summary>
            Updates bid level in order book
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.UpdateAskLevel(System.Decimal,System.Int64)">
            <summary>
            Updates ask level in order book
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.BuildDepthSnapshot(System.DateTime)">
            <summary>
            Builds current depth snapshot with top 5 levels
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.AnalyzeDepthChanges(SmartVolumeStrategy.ATAS.Models.MarketDepth)">
            <summary>
            Analyzes depth changes for significant events
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.CheckForLargeLevelChanges(SmartVolumeStrategy.ATAS.Models.MarketDepth)">
            <summary>
            Checks for large volume changes at price levels
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.CalculateImbalanceSeverity(System.Double)">
            <summary>
            Calculates imbalance severity
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.GetCurrentDepth">
            <summary>
            Gets current order book snapshot
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.GetPerformanceStats">
            <summary>
            Gets performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.TrackPerformance(System.Int64,System.Int64)">
            <summary>
            Tracks processing performance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.Clear">
            <summary>
            Clears all depth data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.MarketDepth.MarketDepthProcessor.Dispose">
            <summary>
            Disposes the processor
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.MarketDepth.DepthChangeEventArgs">
            <summary>
            Depth change event arguments
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.MarketDepth.ImbalanceEventArgs">
            <summary>
            Imbalance event arguments
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.MarketDepth.ImbalanceSeverity">
            <summary>
            Imbalance severity levels
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.EnhancedMarketData">
            <summary>
            Enhanced market data with Phase 3 features: market depth, volume profiles, order flow
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.EnhancedMarketData.Depth">
            <summary>
            Market depth information (top 5 levels)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.EnhancedMarketData.Profile">
            <summary>
            Volume profile data
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.EnhancedMarketData.OrderFlow">
            <summary>
            Order flow analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.EnhancedMarketData.Liquidity">
            <summary>
            Liquidity analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.MarketDepth">
            <summary>
            Market depth representation with top 5 bid/ask levels
            Phase 3: Real-time order book analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.Bids">
            <summary>
            Bid levels (price descending)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.Asks">
            <summary>
            Ask levels (price ascending)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.Timestamp">
            <summary>
            Timestamp of depth snapshot
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.TotalBidVolume">
            <summary>
            Total bid volume (all levels)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.TotalAskVolume">
            <summary>
            Total ask volume (all levels)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.ImbalanceRatio">
            <summary>
            Order book imbalance ratio (bid/ask volume)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.Spread">
            <summary>
            Spread between best bid and ask
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketDepth.MidPrice">
            <summary>
            Mid price from best bid/ask
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.PriceLevel">
            <summary>
            Price level in order book
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.PriceLevel.Price">
            <summary>
            Price level
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.PriceLevel.Volume">
            <summary>
            Volume at this price level
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.PriceLevel.OrderCount">
            <summary>
            Number of orders at this level
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.PriceLevel.AverageOrderSize">
            <summary>
            Average order size at this level
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.VolumeProfile">
            <summary>
            Volume profile analysis for ATAS integration
            Phase 3: Volume-at-price analysis
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfile.Levels">
            <summary>
            Volume profile levels
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfile.PointOfControl">
            <summary>
            Point of control (highest volume price)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfile.ValueAreaHigh">
            <summary>
            Value area high (70% volume upper bound)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfile.ValueAreaLow">
            <summary>
            Value area low (70% volume lower bound)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfile.TotalVolume">
            <summary>
            Total volume in profile
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfile.TimeRange">
            <summary>
            Profile time range
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel">
            <summary>
            Volume profile level with buy/sell breakdown
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel.Price">
            <summary>
            Price level
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel.BuyVolume">
            <summary>
            Buy volume at this price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel.SellVolume">
            <summary>
            Sell volume at this price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel.TotalVolume">
            <summary>
            Total volume at this price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel.VolumeDelta">
            <summary>
            Volume delta (buy - sell)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel.BuySellRatio">
            <summary>
            Buy/sell ratio
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.OrderFlowData">
            <summary>
            Enhanced order flow data for Phase 3
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.Price">
            <summary>
            Trade price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.Volume">
            <summary>
            Trade volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.Direction">
            <summary>
            Trade direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.Timestamp">
            <summary>
            Trade timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.IsAggressive">
            <summary>
            Whether trade was aggressive (market order)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.IsLargeOrder">
            <summary>
            Whether this is a large order (above threshold)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.SizeMultiplier">
            <summary>
            Order size relative to average
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.PriceImpact">
            <summary>
            Price impact of the trade
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.OrderFlowData.TimeSinceLastTrade">
            <summary>
            Time since last trade at this price
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.LiquidityAnalysis">
            <summary>
            Liquidity analysis for market depth
            Phase 3: Enhanced liquidity detection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityAnalysis.LiquidityScore">
            <summary>
            Liquidity score (0-100)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityAnalysis.BidLiquidity">
            <summary>
            Bid liquidity within spread
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityAnalysis.AskLiquidity">
            <summary>
            Ask liquidity within spread
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityAnalysis.Gaps">
            <summary>
            Liquidity gaps detected
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityAnalysis.EstimatedMarketImpact">
            <summary>
            Market impact estimate for standard trade size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityAnalysis.EstimatedFillTime">
            <summary>
            Time to fill estimate for standard order
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.LiquidityGap">
            <summary>
            Liquidity gap in order book
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityGap.StartPrice">
            <summary>
            Gap start price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityGap.EndPrice">
            <summary>
            Gap end price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityGap.GapSize">
            <summary>
            Gap size in price units
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityGap.Direction">
            <summary>
            Gap direction (up/down)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.LiquidityGap.AverageVolume">
            <summary>
            Average volume in gap area
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.MarketMicrostructure">
            <summary>
            Market microstructure analysis
            Phase 3: Advanced market structure detection
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketMicrostructure.Regime">
            <summary>
            Current market regime
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketMicrostructure.Volatility">
            <summary>
            Volatility estimate
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketMicrostructure.TrendStrength">
            <summary>
            Trend strength
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketMicrostructure.MeanReversionProbability">
            <summary>
            Mean reversion probability
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.MarketMicrostructure.BreakoutProbability">
            <summary>
            Breakout probability
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.MarketRegime">
            <summary>
            Market regime classification
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Models.TimeRange">
            <summary>
            Time range for volume profiles and analysis periods
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.TimeRange.Start">
            <summary>
            Start time of the range
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.TimeRange.End">
            <summary>
            End time of the range
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Models.TimeRange.Duration">
            <summary>
            Duration of the time range
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Models.TimeRange.#ctor(System.DateTime,System.DateTime)">
            <summary>
            Initializes a new time range
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer">
            <summary>
            CRITICAL ORDER FLOW ENHANCEMENT: Delta Analysis for Optimal Entries
            Analyzes buying vs selling pressure to identify high-probability entry points
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.ProcessTrade(System.Decimal,System.Int64,SmartVolumeStrategy.Core.Models.TradeDirection,System.DateTime)">
            <summary>
            Processes trade data and updates delta calculations - ENHANCED FOR SCALPING
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.CompleteDeltaBar(System.DateTime)">
            <summary>
            Completes current delta bar and starts new one
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.CompleteMicroDeltaBar(System.DateTime)">
            <summary>
            Completes current micro delta bar and starts new one
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.AnalyzeDeltaSignal(System.Decimal,System.DateTime,System.Int64)">
            <summary>
            Analyzes delta for high-probability signals - ENHANCED FOR SCALPING
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetectBullishDivergence(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.OrderFlow.DeltaReading},System.Decimal)">
            <summary>
            Detects bullish delta divergence (price down, delta up)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetectBearishDivergence(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.OrderFlow.DeltaReading},System.Decimal)">
            <summary>
            Detects bearish delta divergence (price up, delta down)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetectDeltaExhaustion(System.Int64)">
            <summary>
            Detects delta exhaustion patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetectDeltaConfirmation(System.Int64)">
            <summary>
            Detects strong delta confirmation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.CalculateSlope(System.Collections.Generic.List{System.Double})">
            <summary>
            Calculates slope of a series
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetermineSignalDirection(System.ValueTuple{System.Boolean,System.Double}[])">
            <summary>
            Determines overall signal direction
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.CalculateSignalConfidence(System.ValueTuple{System.Boolean,System.Double}[])">
            <summary>
            Calculates overall signal confidence
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetermineSignalType(System.ValueTuple{System.Boolean,System.Double}[])">
            <summary>
            Determines signal type
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetectMicroMomentum(System.Decimal,System.DateTime,System.Int64)">
            <summary>
            ENHANCED: Detects micro-timeframe momentum for ultra-fast scalping
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.DetectInstitutionalFlow(System.Int64,System.DateTime)">
            <summary>
            ENHANCED: Detects institutional flow patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.DeltaAnalyzer.CalculateDeltaVelocity(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.OrderFlow.DeltaReading})">
            <summary>
            Calculates delta velocity for momentum detection
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.DeltaReading">
            <summary>
            Delta reading for historical analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.DeltaSignal">
            <summary>
            Delta signal result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.DeltaSignalType">
            <summary>
            Types of delta signals - ENHANCED FOR SCALPING
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer">
            <summary>
            Enhanced order flow analyzer for Phase 3
            Provides real-time order flow analysis with aggressive/passive detection,
            volume delta analysis, and large order identification
            Target: under 3ms per trade analysis
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.LargeOrderDetected">
            <summary>
            Event fired when large order detected
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.VolumeImbalanceDetected">
            <summary>
            Event fired when significant volume imbalance detected
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.AggressiveTradingDetected">
            <summary>
            Event fired when aggressive trading pattern detected
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer})">
            <summary>
            Initializes the enhanced order flow analyzer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.AnalyzeTrade(System.Decimal,System.Int64,SmartVolumeStrategy.Core.Models.TradeDirection,System.Boolean,System.DateTime)">
            <summary>
            Analyzes trade for order flow patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.CalculateSizeMultiplier(System.Int64)">
            <summary>
            Calculates trade size multiplier relative to average
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.CalculatePriceImpact(System.Decimal)">
            <summary>
            Calculates price impact of trade
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.UpdateStatistics(System.Int64,SmartVolumeStrategy.Core.Models.TradeDirection,System.Decimal,System.DateTime)">
            <summary>
            Updates running statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.UpdateVolumeDelta(System.Decimal,System.Int64,SmartVolumeStrategy.Core.Models.TradeDirection)">
            <summary>
            Updates volume delta at specific price level
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.StoreRecentTrade(SmartVolumeStrategy.ATAS.Models.OrderFlowData)">
            <summary>
            Stores recent trade for pattern analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.AnalyzePatterns(SmartVolumeStrategy.ATAS.Models.OrderFlowData)">
            <summary>
            Analyzes order flow for significant patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.CheckVolumeImbalance">
            <summary>
            Checks for volume imbalance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.CheckAggressiveTrading(SmartVolumeStrategy.ATAS.Models.OrderFlowData)">
            <summary>
            Checks for aggressive trading patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.GetVolumeDeltaSummary">
            <summary>
            Gets current volume delta summary
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.GetPriceDeltas">
            <summary>
            Gets volume delta at specific price levels
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.GetAggressiveTradingStats">
            <summary>
            Gets recent aggressive trading statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.TrackPerformance(System.Int64,System.Int64)">
            <summary>
            Tracks processing performance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.GetPerformanceStats">
            <summary>
            Gets performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.Reset">
            <summary>
            Resets analysis statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedOrderFlowAnalyzer.Dispose">
            <summary>
            Disposes the analyzer
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator">
            <summary>
            CRITICAL ORDER FLOW INTEGRATION: Enhanced Signal Generator
            Combines existing technical indicators with advanced order flow analysis
            Target: 95%+ confidence signals with optimal entry timing
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.ConfidenceThreshold">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.HighConfidenceSignalGenerated">
            <inheritdoc />
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.StatisticsUpdated">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.GenerateSignalAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.GenerateSignalBatchAsync(System.Collections.Generic.IEnumerable{SmartVolumeStrategy.Core.Models.MarketData},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.UpdateMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.SetConfidenceThreshold(System.Double)">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.ProcessOrderFlowData(SmartVolumeStrategy.ATAS.Platform.OrderFlowData)">
            <summary>
            Processes order flow data directly (public accessor for ATAS strategy)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.ProcessOrderBookUpdate(System.Decimal,System.Int64,System.Boolean,System.DateTime)">
            <summary>
            Processes order book updates directly (public accessor for ATAS strategy)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.SetIndicatorAlignmentThreshold(System.Int32)">
            <summary>
            Sets the minimum indicator alignment requirement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.GetStatistics">
            <inheritdoc />
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.SetSmartSelectionEnabled(System.Boolean)">
            <summary>
            Enables or disables smart indicator selection (forwards to base generator)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.SetUserPreferences(SmartVolumeStrategy.Core.Models.UserIndicatorPreferences)">
            <summary>
            Sets user preferences for smart indicator selection (forwards to base generator)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.GetOrderFlowConfirmation(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Gets order flow confirmation for a technical signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.EnhanceSignalWithOrderFlow(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowConfirmation,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Enhances base signal with order flow confirmation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.ConvertToTradeDirection(SmartVolumeStrategy.Core.Models.SignalDirection)">
            <summary>
            Converts SignalDirection to TradeDirection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.DetermineConfirmationType(SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignal)">
            <summary>
            Determines the type of order flow confirmation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.LogEnhancedSignal(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowConfirmation)">
            <summary>
            Logs enhanced signal details
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.CreateErrorSignal(SmartVolumeStrategy.Core.Models.MarketData,System.String)">
            <summary>
            Creates error signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.TrackProcessingTime(System.DateTime)">
            <summary>
            Tracks processing time for performance monitoring
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator.Dispose">
            <inheritdoc />
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowConfirmation">
            <summary>
            Order flow confirmation result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector">
            <summary>
            CRITICAL ORDER FLOW ENHANCEMENT: Order Book Imbalance Detection
            Identifies supply/demand imbalances for optimal entry timing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.ProcessOrderBookUpdate(System.Decimal,System.Int64,System.Boolean,System.DateTime)">
            <summary>
            Processes order book update and detects imbalances
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.CalculateOrderBookImbalance">
            <summary>
            Calculates current order book imbalance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.AnalyzeImbalanceSignal(SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalance,System.DateTime)">
            <summary>
            Analyzes imbalance for trading signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.DetectImbalanceShift(SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalance,System.DateTime)">
            <summary>
            Detects sudden shifts in order book imbalance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.CalculateImbalanceConfidence(System.Double,System.Double)">
            <summary>
            Calculates confidence based on imbalance strength
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.GetCurrentSnapshot">
            <summary>
            Gets current order book snapshot
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.DetectImbalanceVelocity(SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalance,System.DateTime)">
            <summary>
            ENHANCED: Detects imbalance velocity for rapid scalping signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalanceDetector.DetectSustainedPressure(SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalance,System.DateTime)">
            <summary>
            ENHANCED: Detects sustained pressure for institutional flow
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.ImbalanceReading">
            <summary>
            Imbalance reading for historical analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.ImbalanceSignal">
            <summary>
            Order book imbalance signal
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.ImbalanceType">
            <summary>
            Types of imbalance signals - ENHANCED FOR SCALPING
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookSnapshot">
            <summary>
            Order book snapshot
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderBookImbalance">
            <summary>
            Order book imbalance data
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest">
            <summary>
            Integration test for order flow enhancement system
            Validates that technical indicators + order flow analysis work together
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures services for testing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest.TestOrderFlowIntegration">
            <summary>
            Tests the complete order flow integration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest.TestBasicSignalGeneration(SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator)">
            <summary>
            Tests basic signal generation with market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest.TestOrderFlowProcessing(SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator)">
            <summary>
            Tests order flow data processing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest.TestOrderBookProcessing(SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator)">
            <summary>
            Tests order book update processing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest.TestPerformanceRequirements(SmartVolumeStrategy.ATAS.OrderFlow.EnhancedSignalGenerator)">
            <summary>
            Tests performance requirements (less than 20ms total processing)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowIntegrationTest.RunTest">
            <summary>
            Runs the integration test
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator">
            <summary>
            CRITICAL ORDER FLOW ENHANCEMENT: Advanced Signal Generator
            Combines multiple order flow indicators for optimal entry signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.ProcessOrderFlow(SmartVolumeStrategy.ATAS.Platform.OrderFlowData)">
            <summary>
            Processes order flow data and generates high-probability signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.ProcessOrderBookUpdate(System.Decimal,System.Int64,System.Boolean,System.DateTime)">
            <summary>
            Processes order book updates for imbalance detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.CombineOrderFlowSignals(SmartVolumeStrategy.ATAS.OrderFlow.DeltaSignal,SmartVolumeStrategy.ATAS.OrderFlow.AbsorptionSignal,SmartVolumeStrategy.ATAS.Platform.OrderFlowData)">
            <summary>
            Combines multiple order flow signals for optimal entry detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.CreateCombinedSignal(System.Collections.Generic.List{System.ValueTuple{System.Boolean,SmartVolumeStrategy.Core.Models.TradeDirection,System.Double,SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalType}},SmartVolumeStrategy.Core.Models.TradeDirection,SmartVolumeStrategy.ATAS.Platform.OrderFlowData)">
            <summary>
            Creates combined signal from aligned indicators
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.GetLatestOptimalSignal">
            <summary>
            Gets the latest high-confidence order flow signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.GetRecentSignals(System.TimeSpan)">
            <summary>
            Gets recent order flow signals for analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.LogOrderFlowSignal(SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignal)">
            <summary>
            Logs order flow signal details
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.ConvertDirection(SmartVolumeStrategy.Core.Models.TradeDirection)">
            <summary>
            Converts platform direction to core direction
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalGenerator.SetIndicatorAlignmentThreshold(System.Int32)">
            <summary>
            Sets the minimum indicator alignment requirement for signal generation
            </summary>
            <param name="threshold">Minimum number of indicators that must agree (2-9)</param>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignal">
            <summary>
            Combined order flow signal
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.OrderFlowSignalType">
            <summary>
            Types of order flow signals
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.AbsorptionDetector">
            <summary>
            ENHANCED ABSORPTION DETECTOR - Identifies large order absorption patterns
            Detects when large orders are being absorbed without significant price movement
            Critical for identifying institutional accumulation/distribution
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.AbsorptionSignal">
            <summary>
            Absorption signal result - ENHANCED FOR SCALPING
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.AbsorptionType">
            <summary>
            Types of absorption patterns
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.VolumeAtPrice">
            <summary>
            Volume tracking at specific price levels
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.OrderFlow.AbsorptionReading">
            <summary>
            Absorption reading for historical analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation">
            <summary>
            Poolable calculation object for performance optimization
            Used with ObjectPool to reduce memory allocations during high-frequency processing
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.MarketData">
            <summary>
            Market data for calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.Timestamp">
            <summary>
            Calculation timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.BarIndex">
            <summary>
            Bar index for bar-based calculations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.BarValue">
            <summary>
            Bar value for bar-based calculations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.Results">
            <summary>
            Calculation results storage
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.Metrics">
            <summary>
            Performance metrics for this calculation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.IsValid">
            <summary>
            Whether this calculation is valid
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.ErrorMessage">
            <summary>
            Error message if calculation failed
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.Reset">
            <summary>
            Resets the calculation object for reuse
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.SetMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Sets market data for tick-based calculations
            </summary>
            <param name="marketData">Market data</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.SetBarData(System.Int32,System.Decimal)">
            <summary>
            Sets bar data for bar-based calculations
            </summary>
            <param name="barIndex">Bar index</param>
            <param name="barValue">Bar value</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.AddResult(System.String,System.Object)">
            <summary>
            Adds a calculation result
            </summary>
            <param name="key">Result key</param>
            <param name="value">Result value</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.GetResult``1(System.String)">
            <summary>
            Gets a calculation result
            </summary>
            <typeparam name="T">Result type</typeparam>
            <param name="key">Result key</param>
            <returns>Result value or default</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation.SetError(System.String)">
            <summary>
            Marks calculation as failed
            </summary>
            <param name="errorMessage">Error message</param>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics">
            <summary>
            Performance metrics for calculations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.StartTime">
            <summary>
            Calculation start time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.EndTime">
            <summary>
            Calculation end time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.DurationMs">
            <summary>
            Calculation duration in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.IndicatorsProcessed">
            <summary>
            Number of indicators processed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.MemoryBefore">
            <summary>
            Memory usage before calculation (bytes)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.MemoryAfter">
            <summary>
            Memory usage after calculation (bytes)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.MemoryAllocated">
            <summary>
            Memory allocated during calculation (bytes)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.ExceededTargets">
            <summary>
            Whether calculation exceeded performance targets
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.ExceededTarget">
            <summary>
            Performance target that was exceeded (if any)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.Start">
            <summary>
            Starts performance measurement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.End">
            <summary>
            Ends performance measurement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.CalculationMetrics.Reset">
            <summary>
            Resets metrics for reuse
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.OptimizedIndicatorProcessor">
            <summary>
            Optimized indicator processor for high-performance ATAS integration
            Uses object pooling, parallel processing, and memory optimization techniques
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.OptimizedIndicatorProcessor.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.ATAS.Performance.OptimizedIndicatorProcessor},Microsoft.Extensions.ObjectPool.ObjectPool{SmartVolumeStrategy.ATAS.Performance.IndicatorCalculation})">
            <summary>
            Initializes the optimized indicator processor
            </summary>
            <param name="logger">Logger instance</param>
            <param name="calculationPool">Object pool for calculations</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.OptimizedIndicatorProcessor.ProcessTickOptimizedAsync(SmartVolumeStrategy.ATAS.Platform.IMarketDataArg,System.Collections.Generic.IList{SmartVolumeStrategy.Core.Indicators.IIndicator})">
            <summary>
            Processes tick data with optimized performance
            </summary>
            <param name="tick">Market data tick</param>
            <param name="indicators">Indicators to update</param>
            <returns>Processing task</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.OptimizedIndicatorProcessor.ProcessBarOptimizedAsync(System.Int32,System.Decimal,System.Collections.Generic.IList{SmartVolumeStrategy.Core.Indicators.IIndicator})">
            <summary>
            Processes bar data with optimized performance
            </summary>
            <param name="barIndex">Bar index</param>
            <param name="barValue">Bar value</param>
            <param name="indicators">Indicators to update</param>
            <returns>Processing task</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.OptimizedIndicatorProcessor.GetStatistics">
            <summary>
            Gets current performance statistics
            </summary>
            <returns>Performance statistics</returns>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.PerformanceMetrics">
            <summary>
            Performance metrics for tracking
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.ProcessorStatistics">
            <summary>
            Processor performance statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor">
            <summary>
            Phase 2 Performance Monitor - Validates optimization targets and tracks system performance
            Targets: under 2ms conversion time, under 20ms total processing, under 1ms queuing time
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor})">
            <summary>
            Initializes the Phase 2 performance monitor
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor.RecordOperation(System.Int64,System.Int64,System.String)">
            <summary>
            Records a performance measurement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor.GetCurrentStats">
            <summary>
            Gets current performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor.GetRecentTrend(System.TimeSpan)">
            <summary>
            Gets recent performance trend
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor.ReportPerformanceMetrics(System.Object)">
            <summary>
            Periodic performance reporting
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceMonitor.Dispose">
            <summary>
            Disposes the performance monitor
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.PerformanceSnapshot">
            <summary>
            Performance snapshot for trend analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.Phase2PerformanceStats">
            <summary>
            Phase 2 performance statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Performance.PerformanceTrend">
            <summary>
            Performance trend analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge">
            <summary>
            High-performance bridge for converting ATAS market data to Core system format
            Phase 2 Optimizations: Object pooling, lock-free performance tracking, ATAS profiling integration
            Target: less than 2ms conversion time with under 20ms total processing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.#ctor(SmartVolumeStrategy.Core.Interfaces.ISignalGenerator,Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge},Microsoft.Extensions.ObjectPool.ObjectPool{System.Collections.Generic.Dictionary{System.String,System.Object}},ATAS.Indicators.BaseIndicator)">
            <summary>
            Initializes the high-performance ATAS market data bridge
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.CreateDefaultDictionaryPool">
            <summary>
            Creates default dictionary pool for object reuse
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ConvertToCore(ATAS.Indicators.MarketDataArg)">
            <summary>
            Converts ATAS MarketDataArg to Core MarketData with Phase 2 optimizations
            Target: less than 1ms conversion time with object pooling and high-resolution timing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ProcessRealTimeData(ATAS.Indicators.MarketDataArg)">
            <summary>
            Processes real-time ATAS data using high-performance channel pipeline
            Phase 2 Target: less than 1ms queuing time, under 20ms total processing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ProcessMarketDepthChange(ATAS.Indicators.MarketDataArg)">
            <summary>
            Processes market depth changes with enhanced order flow analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ProcessOrderBookUpdate(SmartVolumeStrategy.Core.Models.MarketData,ATAS.Indicators.MarketDataArg)">
            <summary>
            Processes order book updates for enhanced market analysis
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ConvertTradeDirectionOptimized(System.Object)">
            <summary>
            PERFORMANCE FIX: Ultra-fast TradeDirection conversion with minimal allocations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ConvertMarketDataTypeOptimized(System.Boolean,System.Boolean)">
            <summary>
            Optimized MarketDataType conversion using boolean flags
            Phase 2: Eliminates property access overhead
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ConvertTradeDirection(System.Object)">
            <summary>
            Legacy conversion method for backward compatibility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ConvertMarketDataType(ATAS.Indicators.MarketDataArg)">
            <summary>
            Legacy conversion method for backward compatibility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.TrackPerformanceOptimized(System.Int64,System.Int64)">
            <summary>
            High-resolution performance tracking using lock-free operations
            Phase 2 optimization: Eliminates lock contention
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.TrackPerformance(System.Int64)">
            <summary>
            Legacy performance tracking for backward compatibility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.GetPerformanceStats">
            <summary>
            Gets performance statistics with high-resolution timing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.Dispose">
            <summary>
            Disposes the bridge and reports final performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ATASMarketDataBridge.ProcessChannelDataAsync">
            <summary>
            Async processing pipeline for high-throughput data handling
            Phase 2: Eliminates Task.Run overhead with dedicated channel processing
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.OrderFlowData">
            <summary>
            Order flow data structure for enhanced analysis
            Phase 2: Optimized for high-frequency order flow processing
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderFlowData.Price">
            <summary>
            Trade price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderFlowData.Volume">
            <summary>
            Trade volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderFlowData.Direction">
            <summary>
            Trade direction (Buy/Sell/Unknown)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderFlowData.Timestamp">
            <summary>
            Trade timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderFlowData.IsAggressive">
            <summary>
            Whether this trade was aggressive (market order vs limit order)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.IOrderFlowAnalyzer">
            <summary>
            Interface for order flow analysis capabilities
            Phase 2: Enhanced with high-frequency order flow processing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IOrderFlowAnalyzer.ProcessOrderFlow(SmartVolumeStrategy.ATAS.Platform.OrderFlowData)">
            <summary>
            Processes order flow data for enhanced market analysis
            </summary>
            <param name="orderFlowData">Order flow data to analyze</param>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.DictionaryPooledObjectPolicy">
            <summary>
            Object pool policy for Dictionary reuse to reduce GC pressure
            Phase 2 Performance Optimization: Eliminates dictionary allocation overhead
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.DictionaryPooledObjectPolicy.Create">
            <summary>
            Creates a new dictionary instance for the pool
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.DictionaryPooledObjectPolicy.Return(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Resets dictionary state when returning to pool
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy">
            <summary>
            ATAS ChartStrategy interface abstraction for testability and platform independence
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.Name">
            <summary>
            Strategy name displayed in ATAS
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.Description">
            <summary>
            Strategy description displayed in ATAS
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.State">
            <summary>
            Current strategy state
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.DataPath">
            <summary>
            Data path for file operations
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.InstrumentInfo">
            <summary>
            Current instrument information
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.TradingManager">
            <summary>
            Trading manager for order operations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.OnStateChanged">
            <summary>
            Called when strategy state changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.OnCalculate(System.Int32,System.Decimal)">
            <summary>
            Called on each bar calculation
            </summary>
            <param name="bar">Bar index</param>
            <param name="value">Bar value</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.OnNewTrade(SmartVolumeStrategy.ATAS.Platform.IMarketDataArg)">
            <summary>
            Called on each new trade
            </summary>
            <param name="trade">Trade data</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.OnBestBidAskChanged(SmartVolumeStrategy.ATAS.Platform.IMarketDataArg)">
            <summary>
            Called when best bid/ask changes
            </summary>
            <param name="arg">Market data argument</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.OnOrderUpdated(SmartVolumeStrategy.ATAS.Platform.IOrder)">
            <summary>
            Called when order is updated
            </summary>
            <param name="order">Updated order</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.OpenOrder(SmartVolumeStrategy.ATAS.Platform.OrderSide,SmartVolumeStrategy.ATAS.Platform.OrderType,System.Decimal,System.Nullable{System.Decimal},System.Nullable{System.Decimal})">
            <summary>
            Opens a new order
            </summary>
            <param name="side">Order side (Buy/Sell)</param>
            <param name="type">Order type</param>
            <param name="quantity">Order quantity</param>
            <param name="stopLoss">Stop loss price (optional)</param>
            <param name="takeProfit">Take profit price (optional)</param>
            <returns>Created order or null if failed</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.GetCurrentPrice">
            <summary>
            Gets current market price
            </summary>
            <returns>Current price</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.GetCurrentSpread">
            <summary>
            Gets current spread
            </summary>
            <returns>Current spread</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.GetAverageSpread(System.Int32)">
            <summary>
            Gets average spread over specified periods
            </summary>
            <param name="periods">Number of periods</param>
            <returns>Average spread</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.IsMarketHours">
            <summary>
            Checks if market is currently open
            </summary>
            <returns>True if market is open</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.HasMaxPositions">
            <summary>
            Checks if maximum positions reached
            </summary>
            <returns>True if max positions reached</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.GetBasePositionSize">
            <summary>
            Gets base position size
            </summary>
            <returns>Base position size</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.LogInfo(System.String)">
            <summary>
            Logs information message
            </summary>
            <param name="message">Message to log</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.IATASChartStrategy.LogError(System.String,System.Exception)">
            <summary>
            Logs error message
            </summary>
            <param name="message">Error message</param>
            <param name="exception">Optional exception</param>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.StrategyState">
            <summary>
            Strategy state enumeration
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.StrategyState.SetDefaults">
            <summary>
            Setting default values
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.StrategyState.DataLoaded">
            <summary>
            Data has been loaded
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.StrategyState.Running">
            <summary>
            Strategy is running
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.StrategyState.Stopped">
            <summary>
            Strategy is stopped
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.OrderSide">
            <summary>
            Order side enumeration
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderSide.Buy">
            <summary>
            Buy order
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderSide.Sell">
            <summary>
            Sell order
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.OrderType">
            <summary>
            Order type enumeration
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderType.Market">
            <summary>
            Market order
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderType.Limit">
            <summary>
            Limit order
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderType.Stop">
            <summary>
            Stop order
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderType.StopLimit">
            <summary>
            Stop limit order
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg">
            <summary>
            Market data argument interface for ATAS platform
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.Price">
            <summary>
            Trade price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.Volume">
            <summary>
            Trade volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.Time">
            <summary>
            Trade timestamp
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.Direction">
            <summary>
            Trade direction
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.BestBid">
            <summary>
            Best bid price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.BestAsk">
            <summary>
            Best ask price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.BidVolume">
            <summary>
            Bid volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.AskVolume">
            <summary>
            Ask volume
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.TradeId">
            <summary>
            Trade ID
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IMarketDataArg.IsAggressive">
            <summary>
            Whether this is an aggressive trade
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.TradeDirection">
            <summary>
            Trade direction enumeration
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.TradeDirection.Unknown">
            <summary>
            Unknown direction
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.TradeDirection.Buy">
            <summary>
            Buy trade (at ask)
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.TradeDirection.Sell">
            <summary>
            Sell trade (at bid)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo">
            <summary>
            Instrument information interface
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.Symbol">
            <summary>
            Instrument symbol
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.Description">
            <summary>
            Instrument description
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.TickSize">
            <summary>
            Tick size
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.PointValue">
            <summary>
            Point value
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.MinQuantity">
            <summary>
            Minimum quantity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.MaxQuantity">
            <summary>
            Maximum quantity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.QuantityStep">
            <summary>
            Quantity step
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.Currency">
            <summary>
            Currency
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IInstrumentInfo.Exchange">
            <summary>
            Exchange
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.ITradingManager">
            <summary>
            Trading manager interface for order operations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ITradingManager.PlaceOrderAsync(SmartVolumeStrategy.ATAS.Platform.OrderRequest)">
            <summary>
            Places a new order
            </summary>
            <param name="orderRequest">Order request</param>
            <returns>Created order or null if failed</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ITradingManager.ModifyOrderAsync(System.String,System.Nullable{System.Decimal},System.Nullable{System.Decimal})">
            <summary>
            Modifies an existing order
            </summary>
            <param name="orderId">Order ID</param>
            <param name="newPrice">New price</param>
            <param name="newQuantity">New quantity</param>
            <returns>True if successful</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ITradingManager.CancelOrderAsync(System.String)">
            <summary>
            Cancels an order
            </summary>
            <param name="orderId">Order ID</param>
            <returns>True if successful</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ITradingManager.GetCurrentPosition">
            <summary>
            Gets current position
            </summary>
            <returns>Current position</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.ITradingManager.GetActiveOrders">
            <summary>
            Gets all active orders
            </summary>
            <returns>List of active orders</returns>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.OrderRequest">
            <summary>
            Order request for placing orders
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.Side">
            <summary>
            Order side
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.Type">
            <summary>
            Order type
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.Quantity">
            <summary>
            Order quantity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.Price">
            <summary>
            Order price (for limit orders)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.StopPrice">
            <summary>
            Stop price (for stop orders)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.StopLoss">
            <summary>
            Stop loss price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.TakeProfit">
            <summary>
            Take profit price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.OrderRequest.Comment">
            <summary>
            Order comment
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.IOrder">
            <summary>
            Order interface for ATAS platform
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.Id">
            <summary>
            Unique order identifier
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.Side">
            <summary>
            Order side (Buy/Sell)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.Type">
            <summary>
            Order type
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.Quantity">
            <summary>
            Order quantity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.FilledQuantity">
            <summary>
            Filled quantity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.RemainingQuantity">
            <summary>
            Remaining quantity
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.Price">
            <summary>
            Order price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.AverageFillPrice">
            <summary>
            Average fill price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.State">
            <summary>
            Order state
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.CreatedTime">
            <summary>
            Order creation time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.LastUpdateTime">
            <summary>
            Order last update time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.StopLoss">
            <summary>
            Stop loss price (if applicable)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.TakeProfit">
            <summary>
            Take profit price (if applicable)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.Comment">
            <summary>
            Order comment
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IOrder.ErrorMessage">
            <summary>
            Order error message (if any)
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.OrderState">
            <summary>
            Order state enumeration
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderState.Pending">
            <summary>
            Order is pending
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderState.Active">
            <summary>
            Order is active
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderState.PartiallyFilled">
            <summary>
            Order is partially filled
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderState.Filled">
            <summary>
            Order is completely filled
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderState.Cancelled">
            <summary>
            Order is cancelled
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderState.Rejected">
            <summary>
            Order is rejected
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.OrderState.Expired">
            <summary>
            Order has expired
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.IPosition">
            <summary>
            Position interface for ATAS platform
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.Quantity">
            <summary>
            Position quantity (positive for long, negative for short)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.AveragePrice">
            <summary>
            Average entry price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.CurrentPrice">
            <summary>
            Current market price
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.UnrealizedPnL">
            <summary>
            Unrealized profit/loss
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.RealizedPnL">
            <summary>
            Realized profit/loss
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.Side">
            <summary>
            Position side
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.IsFlat">
            <summary>
            Whether position is flat (no position)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.IsLong">
            <summary>
            Whether position is long
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.IsShort">
            <summary>
            Whether position is short
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.CreatedTime">
            <summary>
            Position creation time
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.Platform.IPosition.LastUpdateTime">
            <summary>
            Position last update time
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.PositionSide">
            <summary>
            Position side enumeration
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.PositionSide.Flat">
            <summary>
            No position (flat)
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.PositionSide.Long">
            <summary>
            Long position
            </summary>
        </member>
        <member name="F:SmartVolumeStrategy.ATAS.Platform.PositionSide.Short">
            <summary>
            Short position
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.MockOrder">
            <summary>
            Mock order implementation for development and testing
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.MockPosition">
            <summary>
            Mock position implementation for development and testing
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.MockMarketDataArg">
            <summary>
            Mock market data argument for development and testing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.MockMarketDataArg.CreateTick(System.Decimal,System.Decimal,SmartVolumeStrategy.ATAS.Platform.TradeDirection)">
            <summary>
            Creates a mock market data tick
            </summary>
            <param name="price">Trade price</param>
            <param name="volume">Trade volume</param>
            <param name="direction">Trade direction</param>
            <returns>Mock market data</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.MockMarketDataArg.CreateBidAsk(System.Decimal,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Creates a mock bid/ask update
            </summary>
            <param name="bid">Bid price</param>
            <param name="ask">Ask price</param>
            <param name="bidVolume">Bid volume</param>
            <param name="askVolume">Ask volume</param>
            <returns>Mock market data</returns>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.MockATASChartStrategy">
            <summary>
            Mock ATAS chart strategy base for testing
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.MockInstrumentInfo">
            <summary>
            Mock instrument info implementation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.MockMarketDataGenerator">
            <summary>
            Market data generator for testing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.MockMarketDataGenerator.GenerateTick">
            <summary>
            Generates a realistic market tick
            </summary>
            <returns>Mock market data tick</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.MockMarketDataGenerator.GenerateTicks(System.Int32)">
            <summary>
            Generates a series of market ticks
            </summary>
            <param name="count">Number of ticks to generate</param>
            <returns>List of market ticks</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.MockMarketDataGenerator.ResetPrice(System.Decimal)">
            <summary>
            Resets the price to a specific value
            </summary>
            <param name="price">New price</param>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Platform.TypeAdapters">
            <summary>
            Type adapters for converting between ATAS and Core system types
            Resolves enum conflicts and provides optimized conversion methods
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.TypeAdapters.ToCore(System.Object)">
            <summary>
            Converts ATAS TradeDirection to Core TradeDirection using string-based conversion
            Performance target: less than 1ms
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.TypeAdapters.ToCore(ATAS.Indicators.MarketDataArg)">
            <summary>
            Converts ATAS MarketDataArg to Core MarketDataType based on properties
            Performance target: less than 1ms
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.TypeAdapters.IsValidATASDirection(System.Object)">
            <summary>
            Validates that ATAS direction values are valid
            Used for defensive programming and debugging
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Platform.TypeAdapters.IsValidATASMarketDataArg(ATAS.Indicators.MarketDataArg)">
            <summary>
            Validates that ATAS MarketDataArg is valid
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1">
            <summary>
            High-performance circular buffer for pressure detection data
            Optimized for less than 2ms processing requirements with maximum 1000 data points
            </summary>
            <typeparam name="T">Type of data to store</typeparam>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Capacity">
            <summary>
            Gets the maximum capacity of the buffer
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Count">
            <summary>
            Gets the current number of items in the buffer
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.IsFull">
            <summary>
            Gets whether the buffer is full
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.IsEmpty">
            <summary>
            Gets whether the buffer is empty
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.#ctor(System.Int32)">
            <summary>
            Initializes a new circular buffer with specified capacity
            </summary>
            <param name="capacity">Maximum number of items (max 1000 for memory efficiency)</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Add(`0)">
            <summary>
            Adds an item to the buffer (overwrites oldest if full)
            </summary>
            <param name="item">Item to add</param>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.GetLast">
            <summary>
            Gets the most recent item without removing it
            </summary>
            <returns>Most recent item</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.GetFirst">
            <summary>
            Gets the oldest item without removing it
            </summary>
            <returns>Oldest item</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.GetLast(System.Int32)">
            <summary>
            Gets the last N items from the buffer
            </summary>
            <param name="count">Number of items to get</param>
            <returns>Last N items in chronological order</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Where(System.Func{`0,System.Boolean})">
            <summary>
            Gets items that match the specified predicate
            </summary>
            <param name="predicate">Condition to match</param>
            <returns>Matching items</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Clear">
            <summary>
            Clears all items from the buffer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.ToArray">
            <summary>
            Converts buffer contents to array in chronological order
            </summary>
            <returns>Array of items</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.GetEnumerator">
            <summary>
            Gets enumerator for the buffer (chronological order)
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Average(System.Func{`0,System.Double})">
            <summary>
            Calculates average for numeric types
            </summary>
            <param name="selector">Value selector function</param>
            <returns>Average value</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Sum(System.Func{`0,System.Double})">
            <summary>
            Calculates sum for numeric types
            </summary>
            <param name="selector">Value selector function</param>
            <returns>Sum value</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Max(System.Func{`0,System.Double})">
            <summary>
            Finds maximum value
            </summary>
            <param name="selector">Value selector function</param>
            <returns>Maximum value</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.Min(System.Func{`0,System.Double})">
            <summary>
            Finds minimum value
            </summary>
            <param name="selector">Value selector function</param>
            <returns>Minimum value</returns>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.CircularBuffer`1.StandardDeviation(System.Func{`0,System.Double})">
            <summary>
            Calculates standard deviation
            </summary>
            <param name="selector">Value selector function</param>
            <returns>Standard deviation</returns>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureDetectionEngine">
            <summary>
            Enhanced Pressure Detection Engine for Ultra-Low Timeframe Scalping
            Implements Phase 1 and Phase 2 market pressure detection enhancements
            Target: Less than 2ms processing per detector, 87-92% win rate improvement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureDetectionEngine.ProcessMarketDataAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Processes market data through all pressure detection algorithms
            Target: Less than 20ms total processing time for all detectors combined
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureDetectionEngine.ProcessPhase1Detection(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Phase 1 Detection: Immediate impact features
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureDetectionEngine.ProcessPhase2Detection(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Phase 2 Detection: Advanced analysis features
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureDetectionEngine.CombineDetectionResults(SmartVolumeStrategy.ATAS.PressureDetection.Phase1DetectionResults,SmartVolumeStrategy.ATAS.PressureDetection.Phase2DetectionResults,SmartVolumeStrategy.Core.Models.MarketData,SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilitySignal)">
            <summary>
            Combines detection results into enhanced pressure signal with volatility integration
            CRITICAL ENHANCEMENT: Now includes high volatility detection for precise directional prediction
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureDetectionEngine.ApplyVolatilityConfidenceAdjustment(System.Double,SmartVolumeStrategy.Core.Models.SignalDirection,SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilitySignal)">
            <summary>
            CRITICAL ENHANCEMENT: Applies volatility-based confidence adjustments
            High volatility periods with aligned signals get confidence boost
            Conflicting signals during high volatility get confidence reduction
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureDetectionEngine.TrackPerformance(System.Int64,System.Int64,SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureSignal)">
            <summary>
            Tracks performance metrics for optimization
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector">
            <summary>
            Liquidity Vacuum Detector - Phase 1 Priority 5
            Identifies when bid/ask spread widens 3x+ suddenly or order book depth drops 50%+ rapidly
            Provides early warning of explosive moves and breakouts
            Target: Less than 2ms processing, breakout prediction
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.DetectVacuum(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Detects liquidity vacuum conditions
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.CalculateLiquidityMetrics(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Calculates current liquidity metrics from market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.UpdateBaseline">
            <summary>
            Updates baseline liquidity calculations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.AnalyzeVacuumConditions(SmartVolumeStrategy.ATAS.PressureDetection.LiquidityReading)">
            <summary>
            Analyzes current conditions for vacuum patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.DetermineVacuumType(System.Boolean,System.Boolean)">
            <summary>
            Determines the type of vacuum detected
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.DetermineVacuumDirection(SmartVolumeStrategy.ATAS.PressureDetection.LiquidityReading)">
            <summary>
            Determines likely direction from vacuum characteristics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.CalculateVacuumConfidence(System.Double,System.Double,System.Boolean,SmartVolumeStrategy.Core.Models.SignalDirection)">
            <summary>
            Calculates confidence score for vacuum detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.IsBreakoutPrediction(SmartVolumeStrategy.ATAS.PressureDetection.LiquidityReading,System.Boolean)">
            <summary>
            Checks if this vacuum predicts a breakout
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.IsLikelyFalsePositive(SmartVolumeStrategy.ATAS.PressureDetection.LiquidityReading,System.Double,System.Double)">
            <summary>
            Filters likely false positives
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumDetector.GetStatistics">
            <summary>
            Gets current liquidity statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.StatisticalExtensions">
            <summary>
            Extension methods for statistical calculations
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner">
            <summary>
            Multi-Timeframe Delta Aligner - Phase 1 Priority 3
            Requires alignment across 1s, 3s, 15s, and 1min delta timeframes before generating signals
            Minimum 3 timeframes must agree for signal validation
            Target: Less than 2ms processing, +25-30% signal quality improvement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.AlignTimeframes(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Analyzes delta alignment across multiple timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.UpdateTimeframeDeltas(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates delta calculations for all timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.CalculateTradeDelta(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Calculates delta contribution from current trade
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.ShouldStartNewBar(SmartVolumeStrategy.ATAS.PressureDetection.TimeframeType,System.DateTime,System.TimeSpan)">
            <summary>
            Checks if a new bar should be started for the timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.GetBarStartTime(System.DateTime,System.TimeSpan)">
            <summary>
            Gets the start time for a bar based on timeframe
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.CompleteBar(SmartVolumeStrategy.ATAS.PressureDetection.TimeframeType,System.DateTime)">
            <summary>
            Completes a bar and adds it to history
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.AnalyzeAlignment(System.DateTime)">
            <summary>
            Analyzes alignment across all timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.AnalyzeDirectionalAlignment(System.Collections.Generic.Dictionary{SmartVolumeStrategy.ATAS.PressureDetection.TimeframeType,SmartVolumeStrategy.ATAS.PressureDetection.DeltaReading})">
            <summary>
            Analyzes directional alignment across timeframes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.CalculateAlignmentStrength(System.Collections.Generic.Dictionary{SmartVolumeStrategy.ATAS.PressureDetection.TimeframeType,SmartVolumeStrategy.ATAS.PressureDetection.DeltaReading},SmartVolumeStrategy.Core.Models.SignalDirection)">
            <summary>
            Calculates alignment strength based on delta magnitudes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.CalculateAlignmentConfidence(SmartVolumeStrategy.ATAS.PressureDetection.AlignmentAnalysis,System.Double)">
            <summary>
            Calculates final confidence score for alignment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.MultiTimeframeDeltaAligner.GetStatistics">
            <summary>
            Gets current alignment statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityTracker">
            <summary>
            Pressure Velocity Tracker - Phase 1 Priority 2
            Monitors pressure rate of change and alerts when velocity increases 3x above normal baseline
            Provides 2-5 second early warning of major pressure changes
            Target: Less than 2ms processing, +20-25% signal quality improvement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityTracker.TrackVelocity(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Tracks pressure velocity and detects significant changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityTracker.CalculateMarketPressure(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Calculates market pressure from current market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityTracker.CalculateVelocity(System.Double,System.DateTime)">
            <summary>
            Calculates velocity from pressure change over time
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityTracker.UpdateBaseline">
            <summary>
            Updates baseline velocity and acceleration calculations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityTracker.AnalyzeVelocitySpike(SmartVolumeStrategy.ATAS.PressureDetection.VelocityReading,System.Double,System.DateTime)">
            <summary>
            Analyzes current velocity for significant spikes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityTracker.GetStatistics">
            <summary>
            Gets current velocity statistics for monitoring
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector">
            <summary>
            Print Size Clustering Detector - Phase 1 Priority 1
            Detects when 3+ orders of similar size (±10%) hit the same price level within 5 seconds
            Indicates institutional coordination or large order breaking
            Target: Less than 2ms processing, +15-25% signal quality improvement
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.DetectClustering(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Detects print size clustering patterns in market data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.AnalyzeClustering(SmartVolumeStrategy.ATAS.PressureDetection.TradeData)">
            <summary>
            Analyzes recent trades for clustering patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.AnalyzeSizeClustering(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.PressureDetection.TradeData})">
            <summary>
            Analyzes trades for size clustering (similar sizes within tolerance)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.IsWithinSizeTolerance(System.Int64,System.Int64)">
            <summary>
            Checks if two trade sizes are within tolerance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.CalculateClusterMetrics(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.PressureDetection.TradeData},SmartVolumeStrategy.ATAS.PressureDetection.TradeData)">
            <summary>
            Calculates cluster quality metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.CalculateSizeVariation(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.PressureDetection.TradeData})">
            <summary>
            Calculates size variation within cluster
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.DetermineClusterSignal(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.PressureDetection.TradeData},SmartVolumeStrategy.ATAS.PressureDetection.ClusterMetrics)">
            <summary>
            Determines signal direction and confidence from cluster
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.IsInstitutionalCluster(SmartVolumeStrategy.ATAS.PressureDetection.TradeCluster)">
            <summary>
            Checks if cluster represents institutional activity
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.IsInstitutionalPattern(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.PressureDetection.TradeData},SmartVolumeStrategy.ATAS.PressureDetection.ClusterMetrics)">
            <summary>
            Checks if cluster shows institutional patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.PrintSizeClusteringDetector.CleanOldTrades(System.DateTime)">
            <summary>
            Removes trades outside the time window
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector">
            <summary>
            Volume Rate of Change Detector - Phase 1 Priority 4
            Alerts when 10-second volume exceeds 5x the rolling average, indicating institutional activity
            Target: Less than 2ms processing, institutional flow detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.DetectVolumeSpike(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Detects volume rate of change spikes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.UpdateVolumeWindow(SmartVolumeStrategy.ATAS.PressureDetection.VolumeReading)">
            <summary>
            Updates the current volume window
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.CompleteVolumeWindow(System.DateTime)">
            <summary>
            Completes the current volume window and adds to history
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.UpdateBaseline">
            <summary>
            Updates baseline average volume calculation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.AnalyzeVolumeSpike(SmartVolumeStrategy.ATAS.PressureDetection.VolumeReading)">
            <summary>
            Analyzes current volume for significant spikes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.DetermineVolumeDirection(SmartVolumeStrategy.ATAS.PressureDetection.VolumeReading)">
            <summary>
            Determines volume direction from recent trades
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.CalculateVolumeConfidence(System.Double,System.Boolean,SmartVolumeStrategy.Core.Models.SignalDirection)">
            <summary>
            Calculates confidence score for volume spike
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.IsLikelyFalsePositive(SmartVolumeStrategy.ATAS.PressureDetection.VolumeReading,System.Double)">
            <summary>
            Filters likely false positives
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.ConvertTradeDirectionToSignalDirection(SmartVolumeStrategy.Core.Models.TradeDirection)">
            <summary>
            Converts TradeDirection to SignalDirection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateOfChangeDetector.GetStatistics">
            <summary>
            Gets current volume statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors">
            <summary>
            Demo Data Optimized Phase 2 Detector Configuration
            Provides calibrated thresholds and fallback mechanisms for demo/test market data
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.DemoConfig">
            <summary>
            Demo-optimized configuration for Phase 2 detectors
            Reduces thresholds by 80-95% to account for demo data characteristics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.FallbackMechanisms">
            <summary>
            Fallback mechanisms for when Phase 2 detectors fail to generate signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.FallbackMechanisms.CreateFallbackSignal(SmartVolumeStrategy.Core.Models.MarketData,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Creates a synthetic pressure signal when all Phase 2 detectors fail
            ENHANCED: Improved signal generation with better demo data compatibility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.FallbackMechanisms.CalculateBidAskPressure(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            CRITICAL FIX: Enhanced bid/ask pressure calculation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.FallbackMechanisms.CalculateMomentumSignal(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            CRITICAL FIX: Enhanced momentum signal calculation
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.AdaptiveThresholdManager">
            <summary>
            Adaptive threshold manager for Phase 2 detectors
            Automatically adjusts thresholds based on detection success rates
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.AdaptiveThresholdManager.RecordDetectionAttempt(System.String,System.Boolean,System.Double)">
            <summary>
            Records detection attempt and adjusts thresholds if needed
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.AdaptiveThresholdManager.GetOptimizedThreshold(System.String,System.Double)">
            <summary>
            Gets optimized threshold for a detector
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.AdaptiveThresholdManager.GetOptimizationSummary">
            <summary>
            Gets optimization summary for all detectors
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.DemoDataOptimizedDetectors.DetectorStats">
            <summary>
            Statistics tracker for individual detectors
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory">
            <summary>
            Factory for creating Phase 2 detectors with appropriate configuration for demo vs production data
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.DetectionEnvironment">
            <summary>
            Detection environment configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Create">
            <summary>
            Creates optimized Phase 2 detectors based on environment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Create.IcebergDetector(Microsoft.Extensions.Logging.ILogger,SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.DetectionEnvironment)">
            <summary>
            Creates an Iceberg Order Detector optimized for the specified environment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Create.SpoofingDetector(Microsoft.Extensions.Logging.ILogger,SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.DetectionEnvironment)">
            <summary>
            Creates a Spoofing Detector optimized for the specified environment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Create.BlockTradeIdentifier(Microsoft.Extensions.Logging.ILogger,SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.DetectionEnvironment)">
            <summary>
            Creates a Block Trade Identifier optimized for the specified environment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Create.ZScoreAnalyzer(Microsoft.Extensions.Logging.ILogger,SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.DetectionEnvironment)">
            <summary>
            Creates a Pressure Z-Score Analyzer optimized for the specified environment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Create.VolumeClusteringAnalyzer(Microsoft.Extensions.Logging.ILogger,SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.DetectionEnvironment)">
            <summary>
            Creates a Volume Clustering Analyzer optimized for the specified environment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Create.CompleteSet(Microsoft.Extensions.Logging.ILogger,SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.DetectionEnvironment)">
            <summary>
            Creates a complete set of Phase 2 detectors optimized for the specified environment
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Phase2DetectorSet">
            <summary>
            Container for a complete set of Phase 2 detectors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Phase2DetectorSet.ProcessMarketDataAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Processes market data through all Phase 2 detectors with adaptive threshold management
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.Phase2DetectorSet.GetOptimizationSummary">
            <summary>
            Gets optimization summary for all detectors
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.ConfigurationRecommendations">
            <summary>
            Configuration recommendations for manual threshold adjustment
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.ConfigurationRecommendations.GetDemoOptimizationRecommendations">
            <summary>
            Gets configuration recommendations for demo data optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.Phase2.Phase2DetectorFactory.ConfigurationRecommendations.LogRecommendations(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Logs all configuration recommendations
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.IcebergOrderDetector">
            <summary>
            Phase 2 Detector Placeholders - To be implemented in Phase 2
            These are placeholder implementations to allow compilation
            </summary>
            <summary>
            Iceberg Order Detector - Phase 2 Priority 1
            Identifies large orders broken into smaller pieces by tracking repeated same-size orders at identical price levels
            Target: Less than 2ms processing, institutional order detection
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.SpoofingDetector">
            <summary>
            Spoofing Detector - Phase 2 Priority 2
            Identifies fake orders placed to manipulate market perception through rapid placement/cancellation patterns
            Target: Less than 2ms processing, market manipulation detection
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.BlockTradeIdentifier">
            <summary>
            Block Trade Identifier - Phase 2 Priority 3
            Identifies large institutional trades and block transactions (>$100k USD, >50k volume)
            Target: Less than 2ms processing, institutional activity detection
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.PressureZScoreAnalyzer">
            <summary>
            Pressure Z-Score Analyzer - Phase 2 Priority 4
            Analyzes current market pressure against historical statistical norms using Z-score analysis
            Target: Less than 2ms processing, statistical anomaly detection
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolumeClusteringAnalyzer">
            <summary>
            Volume Clustering Analyzer - Phase 2 Priority 5
            Identifies significant volume concentrations at specific price levels for support/resistance detection
            Target: Less than 2ms processing, volume concentration analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.OrderEvent">
            <summary>
            Supporting classes for Phase 2 detectors
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityFragmentationAnalyzer">
            <summary>
            Liquidity Fragmentation Analyzer - Phase 2 Priority 6
            Detects when liquidity is scattered vs concentrated to predict breakout conditions
            Target: Less than 2ms processing, 5-10% improvement in entry timing
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureSignal">
            <summary>
            Enhanced pressure signal with Phase 1 and Phase 2 detection results
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureSignal.GetVolatilityAdjustedConfidence">
            <summary>
            Gets volatility-adjusted confidence score
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.IPressureDetectionResult">
            <summary>
            Base interface for all pressure detection results
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase1DetectionResults">
            <summary>
            Phase 1 detection results container
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.Phase2DetectionResults">
            <summary>
            Phase 2 detection results container
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.PrintClusteringResult">
            <summary>
            Print size clustering detection result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.PressureVelocityResult">
            <summary>
            Pressure velocity tracking result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.DeltaAlignmentResult">
            <summary>
            Multi-timeframe delta alignment result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolumeRateChangeResult">
            <summary>
            Volume rate of change detection result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityVacuumResult">
            <summary>
            Liquidity vacuum detection result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.IcebergDetectionResult">
            <summary>
            Iceberg order detection result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.SpoofingDetectionResult">
            <summary>
            Spoofing detection result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.BlockTradeResult">
            <summary>
            Block trade identification result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.PressureZScoreResult">
            <summary>
            Pressure Z-Score analysis result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolumeClusteringResult">
            <summary>
            Volume clustering analysis result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityFragmentationResult">
            <summary>
            Liquidity fragmentation analysis result
            Detects when liquidity is scattered vs concentrated to predict breakout conditions
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.FragmentationPattern">
            <summary>
            Fragmentation pattern types detected by liquidity analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.LiquiditySnapshot">
            <summary>
            Represents a snapshot of liquidity at a specific point in time
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityLevel">
            <summary>
            Represents liquidity at a specific price level
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.FragmentationReading">
            <summary>
            Fragmentation reading with calculated metrics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.LiquidityDistribution">
            <summary>
            Liquidity distribution analysis results
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor">
            <summary>
            Advanced Directional Bias Prediction for High Volatility Periods
            Uses order flow, momentum, and institutional activity patterns
            Target: 90%+ directional accuracy during volatility spikes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor.PredictDirectionalBias(SmartVolumeStrategy.Core.Models.MarketData,System.Double)">
            <summary>
            Predicts directional bias during high volatility periods
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor.AnalyzeOrderFlow(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Analyzes order flow for directional bias
            Key: Aggressive vs Passive order ratios, Large order detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor.DetectInstitutionalActivity(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Detects institutional activity patterns
            Key: Block trades, Iceberg orders, Unusual volume patterns
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor.AnalyzeMomentum(SmartVolumeStrategy.Core.Models.MarketData,System.Double)">
            <summary>
            Analyzes momentum during high volatility
            Key: Price acceleration, Volume-weighted momentum, Breakout momentum
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor.AnalyzeVolumeProfile(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Analyzes volume profile for support/resistance levels
            Key: Volume at price levels, POC shifts, Value area changes
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor.CombineDirectionalFactors(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalFactor})">
            <summary>
            Combines directional factors with their weights
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBiasPredictor.CalculatePredictionConfidence(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalFactor},System.Double)">
            <summary>
            Calculates prediction confidence based on factor alignment and volatility
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector">
            <summary>
            High Volatility Detection Engine for Ultra-Precise Directional Prediction
            Combines multiple volatility indicators with order flow analysis
            Target: 90%+ directional accuracy during high volatility periods
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.DetectHighVolatility(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Detects high volatility periods with directional bias prediction
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.CalculateATRVolatility">
            <summary>
            Calculates ATR-based volatility (normalized)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.CalculatePriceVelocity">
            <summary>
            Calculates price velocity (rate of price change)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.CalculateVolumeVolatility">
            <summary>
            Calculates volume volatility
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.CalculateSpreadVolatility">
            <summary>
            Calculates spread volatility (bid-ask spread changes)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.CalculateDeltaVolatility">
            <summary>
            Calculates delta volatility (order flow imbalance)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.CombineVolatilityMeasures(System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Combines multiple volatility measures with weights
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.DetermineVolatilityLevel(System.Double)">
            <summary>
            Determines volatility level based on combined score
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.PredictDirectionalBias(SmartVolumeStrategy.Core.Models.MarketData,System.Double)">
            <summary>
            Predicts directional bias during high volatility periods
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilityDetector.CalculateDirectionalConfidence(SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBias,SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.VolatilityLevel,System.Double)">
            <summary>
            Calculates directional confidence based on bias and volatility
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilitySignal">
            <summary>
            High Volatility Signal with directional prediction
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.HighVolatilitySignal.GetDescription">
            <summary>
            Gets a human-readable description of the volatility signal
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.VolatilityLevel">
            <summary>
            Volatility levels for classification
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalBias">
            <summary>
            Directional bias prediction result
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.DirectionalFactor">
            <summary>
            Individual factor contributing to directional prediction
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.VolatilityReading">
            <summary>
            Volatility reading for historical analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.OrderFlowData">
            <summary>
            Order flow data for analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.InstitutionalActivity">
            <summary>
            Institutional activity detection
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.MomentumReading">
            <summary>
            Momentum reading for trend analysis
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.EnhancedPressureSignal">
            <summary>
            Enhanced pressure signal with volatility integration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.EnhancedPressureSignal.GetVolatilityAdjustedConfidence">
            <summary>
            Gets volatility-adjusted confidence score
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.Phase1DetectionResults">
            <summary>
            Phase 1 detection results container
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.PressureDetection.VolatilityDetection.Phase2DetectionResults">
            <summary>
            Phase 2 detection results container
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector">
            <summary>
            Systematic Bias Corrector - Implements bias corrections for trading strategies
            Addresses systematic biases with balanced thresholds, conservative momentum scaling,
            neutral signal direction logic, and standardized order comment formats
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.ApplyBiasCorrections(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Applies systematic bias corrections to a trading signal
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.ApplyBalancedThresholds(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Applies balanced thresholds (buyThreshold 1.3, sellThreshold 0.7)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.ApplyConservativeMomentumScaling(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Applies conservative momentum scaling (factor of 2 instead of 10)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.ApplyNeutralSignalDirectionLogic(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Applies neutral signal direction logic (greater than 0.52 BUY, less than 0.48 SELL)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.ApplyStandardizedOrderComments(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Applies standardized order comment format 'HPSS-{TYPE}-{TIMESTAMP}'
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.LogBiasCorrectionSummary(SmartVolumeStrategy.Core.Models.TradingSignal,SmartVolumeStrategy.Core.Models.TradingSignal)">
            <summary>
            Logs a summary of bias corrections applied
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.GetBiasCorrectionStatistics">
            <summary>
            Gets bias correction statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection.SystematicBiasCorrector.ResetStatistics">
            <summary>
            Resets bias correction statistics
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult">
            <summary>
            Result of coordinated signal generation combining Phase 1 and Phase 2 detectors
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.IsValid">
            <summary>
            Whether the coordinated signal is valid and meets all requirements
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Direction">
            <summary>
            Final signal direction after coordination
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Confidence">
            <summary>
            Final confidence score (0.0 - 1.0) after weighting and early warning boosts
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Phase1Confidence">
            <summary>
            Phase 1 confidence contribution (40% weight)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Phase2Confidence">
            <summary>
            Phase 2 confidence contribution (60% weight)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.EarlyWarningBoost">
            <summary>
            Early warning boost applied (10-15%)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Phase1Signal">
            <summary>
            Original Phase 1 signal from traditional indicators
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Phase2Signals">
            <summary>
            Phase 2 signals from enhanced pressure detectors
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.ProcessingTimeMs">
            <summary>
            Total processing time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Timestamp">
            <summary>
            Timestamp of signal generation
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.ConflictResolved">
            <summary>
            Whether signal conflicts were resolved during coordination
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.IndicatorAlignment">
            <summary>
            Total number of aligned indicators (Phase 1 + Phase 2)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult.Metadata">
            <summary>
            Additional metadata about the coordination process
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult">
            <summary>
            Wrapper for Phase 2 detector results with unified interface
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.DetectorType">
            <summary>
            Type of Phase 2 detector that generated this signal
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.Direction">
            <summary>
            Signal direction from the detector
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.Confidence">
            <summary>
            Confidence score from the detector (0.0 - 1.0)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.IsEarlyWarning">
            <summary>
            Whether this signal represents an early warning condition
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.Timestamp">
            <summary>
            Timestamp when the signal was generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.RawResult">
            <summary>
            Raw result object from the specific detector
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.ReliabilityScore">
            <summary>
            Reliability score for this detector type (0-100)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult.DetectorMetadata">
            <summary>
            Additional detector-specific metadata
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats">
            <summary>
            Performance statistics for the Signal Coordination System
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.TotalSignalsProcessed">
            <summary>
            Total number of signals processed
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.Phase1Signals">
            <summary>
            Number of valid Phase 1 signals generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.Phase2Signals">
            <summary>
            Number of valid Phase 2 signals generated
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.CombinedSignals">
            <summary>
            Number of valid combined signals that met all requirements
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.ConflictResolutions">
            <summary>
            Number of signal conflicts that were resolved
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.EarlyWarningBoosts">
            <summary>
            Number of early warning boosts applied
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.AverageProcessingTimeMs">
            <summary>
            Average processing time in milliseconds
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.SuccessRate">
            <summary>
            Success rate (valid signals / total signals)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.Phase1ContributionRate">
            <summary>
            Phase 1 contribution rate
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.Phase2ContributionRate">
            <summary>
            Phase 2 contribution rate
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.ConflictResolutionRate">
            <summary>
            Conflict resolution rate
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationStats.EarlyWarningBoostRate">
            <summary>
            Early warning boost rate
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig">
            <summary>
            Configuration for Signal Coordination System
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.Phase1Weight">
            <summary>
            Weight for Phase 1 indicators (default: 0.40)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.Phase2Weight">
            <summary>
            Weight for Phase 2 detectors (default: 0.60)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.EarlyWarningBoostMin">
            <summary>
            Minimum early warning boost (default: 0.10)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.EarlyWarningBoostMax">
            <summary>
            Maximum early warning boost (default: 0.15)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.ConfidenceThreshold">
            <summary>
            Confidence threshold for signal validation (default: 0.85)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.MinIndicatorAlignment">
            <summary>
            Minimum indicator alignment required (default: 3, will be overridden by user configuration)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.MaxProcessingTimeMs">
            <summary>
            Maximum processing time in milliseconds (default: 20)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.ConflictResolutionThreshold">
            <summary>
            Conflict resolution threshold for confidence difference (default: 0.05)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.EnableEarlyWarningBoost">
            <summary>
            Whether to enable early warning boost (default: true)
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.PrioritizeInstitutionalSignals">
            <summary>
            Whether to prioritize institutional signals in conflict resolution (default: true)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationConfig.IsValid">
            <summary>
            Validates the configuration
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationEventArgs">
            <summary>
            Event arguments for signal coordination events
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem">
            <summary>
            Signal Coordination System - Integrates Phase 1 and Phase 2 detectors
            Implements unified signal aggregation with weighted confidence scoring
            Phase 1: 40% weight, Phase 2: 60% weight, Early warnings: +10-15% boost
            CRITICAL: Implements ISignalGenerator for AdaptiveSystemCoordinator integration
            ENHANCED: Supports Smart Indicator Selection for adaptive indicator management
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.SetConfidenceThreshold(System.Double)">
            <summary>
            Sets the confidence threshold for signal validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.SetIndicatorAlignmentThreshold(System.Int32)">
            <summary>
            Sets the indicator alignment threshold for signal validation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.EnableSmartIndicatorSelection(System.Boolean)">
            <summary>
            CRITICAL FIX: Enables Smart Indicator Selection if supported by Phase 1 generator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.GenerateCoordinatedSignalAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            Generates coordinated signal by combining Phase 1 and Phase 2 detectors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.CoordinateSignals(SmartVolumeStrategy.Core.Models.MarketData,SmartVolumeStrategy.Core.Models.TradingSignal,System.Object[],System.Double)">
            <summary>
            Coordinates signals from Phase 1 and Phase 2 detectors
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.ExtractPhase2Signals(System.Object[])">
            <summary>
            Extracts Phase 2 signals from detector results
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.CalculatePhase2Confidence(System.Collections.Generic.List{SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult})">
            <summary>
            Calculates Phase 2 confidence using weighted average
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.CalculateEarlyWarningBoost(SmartVolumeStrategy.Core.Models.TradingSignal,System.Collections.Generic.List{SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult})">
            <summary>
            Calculates early warning boost from any detector flagging early warnings
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.ResolveSignalConflicts(SmartVolumeStrategy.Core.Models.TradingSignal,System.Collections.Generic.List{SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult},System.Double)">
            <summary>
            Resolves conflicts between Phase 1 and Phase 2 signals
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.CheckIndicatorAlignment(SmartVolumeStrategy.Core.Models.TradingSignal,System.Collections.Generic.List{SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult})">
            <summary>
            Checks if indicator alignment requirements are met
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.GetIndicatorAlignmentCount(SmartVolumeStrategy.Core.Models.TradingSignal,System.Collections.Generic.List{SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult})">
            <summary>
            Gets total indicator alignment count
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.CreateMetadata(SmartVolumeStrategy.Core.Models.MarketData,SmartVolumeStrategy.Core.Models.TradingSignal,System.Collections.Generic.List{SmartVolumeStrategy.ATAS.SignalCoordination.Phase2SignalResult},System.Double)">
            <summary>
            Creates metadata for coordinated signal result
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.UpdateStatistics(SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult,System.Double)">
            <summary>
            Updates performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.LogSignalCoordination(SmartVolumeStrategy.ATAS.SignalCoordination.CoordinatedSignalResult)">
            <summary>
            Logs signal coordination details
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.CreateErrorResult(SmartVolumeStrategy.Core.Models.MarketData,System.String)">
            <summary>
            Creates error result for exception handling
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.GetStatistics">
            <summary>
            Gets performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.GetPhase2OptimizationRecommendations">
            <summary>
            CRITICAL DEBUG: Gets Phase 2 detector optimization recommendations for demo data
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.AnalyzeDetectorPerformance">
            <summary>
            Analyzes current Phase 2 detector performance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.CalculateDetectorSuccessRate(System.String)">
            <summary>
            Calculates success rate for a specific detector type
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.GeneratePhase2ActionRecommendations(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Generates specific action recommendations for Phase 2 optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.GenerateSignalAsync(SmartVolumeStrategy.Core.Models.MarketData,System.Threading.CancellationToken)">
            <summary>
            ISignalGenerator implementation - bridges to coordinated signal generation
            CRITICAL: Enables AdaptiveSystemCoordinator integration with LiquidityFragmentationAnalyzer
            </summary>
        </member>
        <member name="P:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.ConfidenceThreshold">
            <summary>
            Confidence threshold property for ISignalGenerator interface
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.GenerateSignalBatchAsync(System.Collections.Generic.IEnumerable{SmartVolumeStrategy.Core.Models.MarketData},System.Threading.CancellationToken)">
            <summary>
            Generates multiple signals for batch processing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.UpdateMarketData(SmartVolumeStrategy.Core.Models.MarketData)">
            <summary>
            Updates signal generator with new market data for real-time processing
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.SmartVolumeStrategy#Core#Interfaces#ISignalGenerator#GetStatistics">
            <summary>
            Gets signal generation statistics compatible with ISignalGenerator interface
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.HighConfidenceSignalGenerated">
            <summary>
            Event fired when a high-confidence signal is generated
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.SignalCoordination.SignalCoordinationSystem.StatisticsUpdated">
            <summary>
            Event fired when signal generation performance metrics are updated
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SimpleTestStrategy">
            <summary>
            Simple Test Strategy for ATAS Platform
            Minimal implementation to test compilation
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SimpleTestStrategy.#ctor">
            <summary>
            Initializes the Simple Test Strategy
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SimpleTestStrategy.OnCalculate(System.Int32,System.Decimal)">
            <summary>
            Main calculation method called by ATAS for each bar
            </summary>
            <param name="bar">Current bar index</param>
            <param name="value">Current bar value</param>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy">
            <summary>
            Enhanced Smart Volume Strategy for ATAS Platform
            Implements proper ExtendedIndicator inheritance with real-time market data callbacks
            Integrates with Core Adaptive System while leveraging native ATAS capabilities
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.OnInitialize">
            <summary>
            Called when the indicator is initialized
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.OnNewTrade(ATAS.Indicators.MarketDataArg)">
            <summary>
            Called for each new trade - implements proper ATAS callback signature
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.OnBestBidAskChanged(ATAS.Indicators.MarketDataArg)">
            <summary>
            Called when best bid/ask changes - implements proper ATAS callback signature
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.OnCalculate(System.Int32,System.Decimal)">
            <summary>
            Called for each bar calculation - required by BaseIndicator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.MarketDepthChanged(ATAS.Indicators.MarketDataArg)">
            <summary>
            Alternative market depth callback for additional depth levels
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.OnDispose">
            <summary>
            Called when the indicator is disposed
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.InitializeCoreIntegration">
            <summary>
            Initializes Core system integration with dependency injection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.ConfigureCoreServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures Core services for dependency injection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.RegisterAdaptiveSystemComponents(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers adaptive system components
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.InitializeDataBridge">
            <summary>
            Initializes the ATAS market data bridge
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.InitializeAdaptiveSystem">
            <summary>
            Initializes the adaptive system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.InitializePhase3Features">
            <summary>
            Initializes Phase 3 enhanced market data features
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.ReportPerformanceIfNeeded">
            <summary>
            Reports performance metrics periodically
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.ReportPerformance">
            <summary>
            Reports current performance metrics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.OnEnhancedDataAvailable(System.Object,SmartVolumeStrategy.ATAS.Integration.EnhancedMarketDataEventArgs)">
            <summary>
            Handles enhanced market data events from Phase 3 coordinator
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.SmartVolumeATASStrategy.OnMarketStructureChanged(System.Object,SmartVolumeStrategy.ATAS.Integration.MarketStructureChangeEventArgs)">
            <summary>
            Handles market structure change events
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Tests.TestRunner">
            <summary>
            Test runner for volatility detection integration tests
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.TestRunner.RunAllTests">
            <summary>
            Runs all integration tests for the volatility detection system
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.TestRunner.Main(System.String[])">
            <summary>
            Entry point for running tests
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Tests.VolatilityDetectionIntegrationTest">
            <summary>
            Integration test for volatility detection system
            Verifies that the enhanced pressure detection engine properly integrates with volatility detection
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.VolatilityDetectionIntegrationTest.TestVolatilityDetectionIntegration">
            <summary>
            Tests the complete volatility detection integration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.VolatilityDetectionIntegrationTest.CreateHighVolatilityMarketData">
            <summary>
            Creates market data with high volatility characteristics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.VolatilityDetectionIntegrationTest.CreateNormalVolatilityMarketData">
            <summary>
            Creates market data with normal volatility characteristics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.VolatilityDetectionIntegrationTest.ValidateIntegrationResults(SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureSignal,SmartVolumeStrategy.ATAS.PressureDetection.EnhancedPressureSignal)">
            <summary>
            Validates that the integration results meet expectations
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.VolatilityDetectionIntegrationTest.TestPerformanceRequirements">
            <summary>
            Runs a performance test to ensure less than 20ms processing requirement
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.Tests.VolatilityTestRunner">
            <summary>
            Simple test runner to verify volatility detection is working
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.VolatilityTestRunner.RunQuickTest">
            <summary>
            Runs a quick test to verify volatility detection integration
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.Tests.VolatilityTestRunner.Main(System.String[])">
            <summary>
            Entry point for the test
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer">
            <summary>
            High-performance volume profile analyzer for ATAS fixed profiles integration
            Phase 3: Volume-at-price analysis with POC, value area, and support/resistance detection
            Target: under 10ms profile calculation, real-time updates
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.POCChanged">
            <summary>
            Event fired when new POC (Point of Control) is detected
            </summary>
        </member>
        <member name="E:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.ValueAreaChanged">
            <summary>
            Event fired when value area changes significantly
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.#ctor(Microsoft.Extensions.Logging.ILogger{SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer},System.Decimal,System.Nullable{System.TimeSpan})">
            <summary>
            Initializes the volume profile analyzer
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.ProcessTrade(System.Decimal,System.Int64,SmartVolumeStrategy.Core.Models.TradeDirection,System.DateTime)">
            <summary>
            Processes trade data and updates volume profile
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.GetCurrentProfile(System.Nullable{System.DateTime})">
            <summary>
            Gets current volume profile with caching optimization
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.CalculatePointOfControl(System.Collections.Generic.IReadOnlyList{SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel})">
            <summary>
            Calculates Point of Control (highest volume price)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.CalculateValueArea(System.Collections.Generic.IReadOnlyList{SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel})">
            <summary>
            Calculates Value Area (70% of total volume)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.CreateNewLevel(System.Decimal,System.Int64,SmartVolumeStrategy.Core.Models.TradeDirection)">
            <summary>
            Creates new volume profile level
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.UpdateExistingLevel(SmartVolumeStrategy.ATAS.Models.VolumeProfileLevel,System.Int64,SmartVolumeStrategy.Core.Models.TradeDirection)">
            <summary>
            Updates existing volume profile level
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.RoundToTickSize(System.Decimal)">
            <summary>
            Rounds price to tick size
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.StartNewProfilePeriod(System.DateTime)">
            <summary>
            Starts new profile period
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.ArchiveProfile(SmartVolumeStrategy.ATAS.Models.VolumeProfile)">
            <summary>
            Archives completed profile (placeholder for future enhancement)
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.GetSupportResistanceLevels">
            <summary>
            Gets support and resistance levels from profile
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.TrackPerformance(System.Int64,System.Int64)">
            <summary>
            Tracks processing performance
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.GetPerformanceStats">
            <summary>
            Gets performance statistics
            </summary>
        </member>
        <member name="M:SmartVolumeStrategy.ATAS.VolumeProfile.VolumeProfileAnalyzer.Dispose">
            <summary>
            Disposes the analyzer
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.VolumeProfile.POCChangeEventArgs">
            <summary>
            POC change event arguments
            </summary>
        </member>
        <member name="T:SmartVolumeStrategy.ATAS.VolumeProfile.ValueAreaChangeEventArgs">
            <summary>
            Value area change event arguments
            </summary>
        </member>
    </members>
</doc>
