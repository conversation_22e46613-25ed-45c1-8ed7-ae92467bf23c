using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Indicators;
using SmartVolumeStrategy.Core.Indicators.Enhanced;
using SmartVolumeStrategy.Core.Indicators.Tier1;
using SmartVolumeStrategy.Core.Indicators.Tier2;
using SmartVolumeStrategy.Core.Interfaces;
using SmartVolumeStrategy.Core.Production;
using SmartVolumeStrategy.Core.Configuration;

namespace SmartVolumeStrategy.Core.Infrastructure.DependencyInjection;

/// <summary>
/// Extension methods for registering indicator services with dependency injection
/// </summary>
public static class IndicatorServiceExtensions
{
    /// <summary>
    /// Registers all indicator services with the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddIndicatorServices(this IServiceCollection services)
    {
        // Register Tier 1 indicators as singletons for performance
        services.AddSingleton<VolumeFlowIndex>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VolumeFlowIndex>>();
            return new VolumeFlowIndex(logger);
        });

        services.AddSingleton<OrderBookPressureRatio>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<OrderBookPressureRatio>>();
            return new OrderBookPressureRatio(logger);
        });

        services.AddSingleton<VWAPDeviationTracker>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VWAPDeviationTracker>>();
            return new VWAPDeviationTracker(logger);
        });

        services.AddSingleton<LargeOrderDetection>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<LargeOrderDetection>>();
            return new LargeOrderDetection(logger);
        });

        services.AddSingleton<BollingerBands>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<BollingerBands>>();
            return new BollingerBands(logger);
        });

        services.AddSingleton<StochasticOscillator>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<StochasticOscillator>>();
            return new StochasticOscillator(logger);
        });

        // Register Tier 2 indicators as singletons
        services.AddSingleton<UltraFastRSI>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<UltraFastRSI>>();
            return new UltraFastRSI(logger);
        });

        services.AddSingleton<TickBasedMACD>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<TickBasedMACD>>();
            return new TickBasedMACD(logger);
        });

        services.AddSingleton<LiquidityGapAnalysis>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<LiquidityGapAnalysis>>();
            return new LiquidityGapAnalysis(logger);
        });

        // Register the high-probability signal generator
        services.AddSingleton<HighProbabilitySignalGenerator>();

        // Register as ISignalGenerator interface
        services.AddSingleton<ISignalGenerator>(provider =>
            provider.GetRequiredService<HighProbabilitySignalGenerator>());

        return services;
    }

    /// <summary>
    /// Registers indicator services with custom configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureIndicators">Action to configure indicators</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddIndicatorServices(
        this IServiceCollection services,
        Action<IndicatorConfiguration> configureIndicators)
    {
        var config = new IndicatorConfiguration();
        configureIndicators(config);

        // Register Tier 1 indicators with custom configuration
        services.AddSingleton<VolumeFlowIndex>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VolumeFlowIndex>>();
            return new VolumeFlowIndex(
                logger,
                config.VolumeFlowIndexPeriod,
                config.VolumeFlowIndexBuyThreshold,
                config.VolumeFlowIndexSellThreshold);
        });

        services.AddSingleton<OrderBookPressureRatio>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<OrderBookPressureRatio>>();
            return new OrderBookPressureRatio(
                logger,
                config.OrderBookPressureLevels,
                config.OrderBookPressureBuyThreshold,
                config.OrderBookPressureSellThreshold,
                config.OrderBookPressureMomentumThreshold);
        });

        services.AddSingleton<VWAPDeviationTracker>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VWAPDeviationTracker>>();
            return new VWAPDeviationTracker(
                logger,
                config.VWAPDeviationPeriod,
                config.VWAPDeviationThreshold);
        });

        services.AddSingleton<LargeOrderDetection>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<LargeOrderDetection>>();
            return new LargeOrderDetection(
                logger,
                config.LargeOrderAveragePeriod,
                config.LargeOrderSizeMultiplier);
        });

        services.AddSingleton<BollingerBands>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<BollingerBands>>();
            return new BollingerBands(
                logger,
                config.BollingerBandsPeriod,
                config.BollingerBandsStdDev);
        });

        services.AddSingleton<StochasticOscillator>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<StochasticOscillator>>();
            return new StochasticOscillator(
                logger,
                config.StochasticOscillatorKPeriod,
                config.StochasticOscillatorDPeriod,
                config.StochasticOscillatorOverboughtThreshold,
                config.StochasticOscillatorOversoldThreshold);
        });

        // Register Tier 2 indicators with custom configuration
        services.AddSingleton<UltraFastRSI>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<UltraFastRSI>>();
            return new UltraFastRSI(
                logger,
                config.UltraFastRSIPeriod,
                config.UltraFastRSIOverboughtThreshold,
                config.UltraFastRSIOversoldThreshold);
        });

        services.AddSingleton<TickBasedMACD>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<TickBasedMACD>>();
            return new TickBasedMACD(
                logger,
                config.TickBasedMACDFastPeriod,
                config.TickBasedMACDSlowPeriod,
                config.TickBasedMACDSignalPeriod);
        });

        services.AddSingleton<LiquidityGapAnalysis>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<LiquidityGapAnalysis>>();
            return new LiquidityGapAnalysis(
                logger,
                config.LiquidityGapPriceRangePercent,
                config.LiquidityGapVolumeThresholdPercent,
                config.LiquidityGapVolumeAveragePeriod);
        });

        // CRITICAL FIX: Register Multi-Timeframe Analysis Engine in core indicators
        services.AddSingleton<MultiTimeframeIndicatorEngine>(provider =>
        {
            try
            {
                // Try to get the logger from DI first
                var loggerFactory = provider.GetService<ILoggerFactory>();
                if (loggerFactory != null)
                {
                    var logger = loggerFactory.CreateLogger<MultiTimeframeIndicatorEngine>();
                    return new MultiTimeframeIndicatorEngine(logger);
                }
                else
                {
                    // Fallback: Create our own logger factory
                    var fallbackLoggerFactory = LoggerFactory.Create(builder =>
                    {
                        builder.AddConsole();
                        builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
                    });
                    var fallbackLogger = fallbackLoggerFactory.CreateLogger<MultiTimeframeIndicatorEngine>();
                    return new MultiTimeframeIndicatorEngine(fallbackLogger);
                }
            }
            catch (Exception ex)
            {
                // Last resort: Create with console logger
                Console.WriteLine($"CRITICAL FIX: Creating MultiTimeframeIndicatorEngine with console logger: {ex.Message}");
                var emergencyLoggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                var emergencyLogger = emergencyLoggerFactory.CreateLogger<MultiTimeframeIndicatorEngine>();
                return new MultiTimeframeIndicatorEngine(emergencyLogger);
            }
        });

        // Register the high-probability signal generator
        services.AddSingleton<HighProbabilitySignalGenerator>();

        // Register as ISignalGenerator interface
        services.AddSingleton<ISignalGenerator>(provider =>
            provider.GetRequiredService<HighProbabilitySignalGenerator>());

        return services;
    }

    /// <summary>
    /// Validates that all required indicator services are registered
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection ValidateIndicatorServices(this IServiceCollection services)
    {
        var serviceProvider = services.BuildServiceProvider();

        try
        {
            // Validate all indicators can be resolved
            serviceProvider.GetRequiredService<VolumeFlowIndex>();
            serviceProvider.GetRequiredService<OrderBookPressureRatio>();
            serviceProvider.GetRequiredService<VWAPDeviationTracker>();
            serviceProvider.GetRequiredService<LargeOrderDetection>();
            serviceProvider.GetRequiredService<BollingerBands>();
            serviceProvider.GetRequiredService<StochasticOscillator>();
            serviceProvider.GetRequiredService<UltraFastRSI>();
            serviceProvider.GetRequiredService<TickBasedMACD>();
            serviceProvider.GetRequiredService<LiquidityGapAnalysis>();
            serviceProvider.GetRequiredService<HighProbabilitySignalGenerator>();
            serviceProvider.GetRequiredService<ISignalGenerator>();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to validate indicator services registration", ex);
        }
        finally
        {
            serviceProvider.Dispose();
        }

        return services;
    }

    /// <summary>
    /// Registers production monitoring and deployment services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddProductionServices(this IServiceCollection services)
    {
        // Register production monitoring dashboard
        services.AddSingleton<ProductionMonitoringDashboard>();

        // Register production deployment validator
        services.AddSingleton<ProductionDeploymentValidator>();

        return services;
    }
}

/// <summary>
/// Configuration class for indicator parameters
/// </summary>
public class IndicatorConfiguration
{
    // Volume Flow Index configuration - CRITICAL FIX: BALANCED THRESHOLDS FOR SCALPING
    public int VolumeFlowIndexPeriod { get; set; } = 3;        // Reduced from 5 for faster response (45s-3min)
    public decimal VolumeFlowIndexBuyThreshold { get; set; } = 62m;  // CRITICAL FIX: Balanced threshold (was 70m)
    public decimal VolumeFlowIndexSellThreshold { get; set; } = 38m; // CRITICAL FIX: Balanced threshold (was 30m)

    // Order Book Pressure Ratio configuration - CRITICAL FIX: BALANCED THRESHOLDS
    public int OrderBookPressureLevels { get; set; } = 3;      // Reduced from 5 for faster calculation
    public double OrderBookPressureBuyThreshold { get; set; } = 1.3;  // CRITICAL FIX: Reduced from 1.8/2.0 to eliminate SELL bias
    public double OrderBookPressureSellThreshold { get; set; } = 0.7; // CRITICAL FIX: Increased from 0.55/0.5 to balance thresholds
    public double OrderBookPressureMomentumThreshold { get; set; } = 0.08; // Reduced from 0.1 for more sensitivity

    // VWAP Deviation Tracker configuration - OPTIMIZED FOR SCALPING
    public int VWAPDeviationPeriod { get; set; } = 6;           // Reduced from 14 for faster VWAP (1.5-6min)
    public decimal VWAPDeviationThreshold { get; set; } = 0.0008m; // Reduced from 0.001m (0.08% vs 0.1%)

    // Large Order Detection configuration - OPTIMIZED FOR SCALPING
    public int LargeOrderAveragePeriod { get; set; } = 30;      // Reduced from 100 for faster baseline (7.5-30min)
    public decimal LargeOrderSizeMultiplier { get; set; } = 4.0m; // Reduced from 5.0m for more sensitivity

    // Bollinger Bands configuration - OPTIMIZED FOR ULTRA-FAST BREAKOUTS
    public int BollingerBandsPeriod { get; set; } = 8;          // Reduced from 20 for faster response (2-8min)
    public decimal BollingerBandsStdDev { get; set; } = 1.8m;   // Reduced from 2.0m for tighter bands

    // Stochastic Oscillator configuration - OPTIMIZED FOR ULTRA-FAST MOMENTUM
    public int StochasticOscillatorKPeriod { get; set; } = 5;   // Reduced from 14 for faster momentum (1.25-5min)
    public int StochasticOscillatorDPeriod { get; set; } = 2;   // Reduced from 3 for faster smoothing
    public decimal StochasticOscillatorOverboughtThreshold { get; set; } = 75m; // Reduced from 80m for earlier signals
    public decimal StochasticOscillatorOversoldThreshold { get; set; } = 25m;   // Increased from 20m for earlier signals

    // Ultra-Fast RSI configuration - OPTIMIZED FOR INSTANT MOMENTUM
    public int UltraFastRSIPeriod { get; set; } = 2;           // Reduced from 3 for near-instant response (30s-2min)
    public decimal UltraFastRSIOverboughtThreshold { get; set; } = 70m; // Increased from 65m for clearer signals
    public decimal UltraFastRSIOversoldThreshold { get; set; } = 30m;   // Reduced from 35m for clearer signals

    // Tick-Based MACD configuration - OPTIMIZED FOR ULTRA-FAST CROSSOVERS
    public int TickBasedMACDFastPeriod { get; set; } = 5;      // Reduced from 12 for faster signals (1.25-5min)
    public int TickBasedMACDSlowPeriod { get; set; } = 13;     // Reduced from 26 for faster crossovers (3.25-13min)
    public int TickBasedMACDSignalPeriod { get; set; } = 4;    // Reduced from 9 for faster signal line (1-4min)

    // Liquidity Gap Analysis configuration - OPTIMIZED FOR FASTER GAP DETECTION
    public decimal LiquidityGapPriceRangePercent { get; set; } = 0.003m; // Reduced from 0.005m (0.3% vs 0.5%)
    public decimal LiquidityGapVolumeThresholdPercent { get; set; } = 0.4m; // Reduced from 0.5m (40% vs 50%)
    public int LiquidityGapVolumeAveragePeriod { get; set; } = 20;       // Reduced from 50 for faster baseline (5-20min)

    /// <summary>
    /// Validates the configuration parameters
    /// </summary>
    /// <returns>True if configuration is valid</returns>
    public bool IsValid()
    {
        return VolumeFlowIndexPeriod > 0 &&
               VolumeFlowIndexBuyThreshold > VolumeFlowIndexSellThreshold &&
               OrderBookPressureLevels > 0 &&
               OrderBookPressureBuyThreshold > OrderBookPressureSellThreshold &&
               VWAPDeviationPeriod > 0 &&
               VWAPDeviationThreshold > 0 &&
               LargeOrderAveragePeriod > 0 &&
               LargeOrderSizeMultiplier > 1 &&
               BollingerBandsPeriod > 0 &&
               BollingerBandsStdDev > 0 &&
               StochasticOscillatorKPeriod > 0 &&
               StochasticOscillatorDPeriod > 0 &&
               StochasticOscillatorOverboughtThreshold > StochasticOscillatorOversoldThreshold &&
               UltraFastRSIPeriod > 0 &&
               UltraFastRSIOverboughtThreshold > UltraFastRSIOversoldThreshold &&
               TickBasedMACDSlowPeriod > TickBasedMACDFastPeriod &&
               TickBasedMACDSignalPeriod > 0 &&
               LiquidityGapPriceRangePercent > 0 &&
               LiquidityGapVolumeThresholdPercent > 0 &&
               LiquidityGapVolumeAveragePeriod > 0;
    }
}
