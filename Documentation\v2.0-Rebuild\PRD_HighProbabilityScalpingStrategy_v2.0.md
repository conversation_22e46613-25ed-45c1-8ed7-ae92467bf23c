# Product Requirements Document (PRD)
# High-Probability Scalping Strategy v2.0

**Version:** 2.0  
**Date:** December 10, 2025  
**Status:** Planning Phase  
**Project Lead:** AI Development Team  

---

## 📋 **EXECUTIVE SUMMARY**

The High-Probability Scalping Strategy v2.0 is a complete architectural rebuild of the existing trading strategy, designed to address critical dependency injection failures, indicator initialization issues, and ATAS platform compatibility problems. This rebuild focuses on creating a robust, maintainable, and high-performance scalping strategy optimized for ultra-low timeframes (15s/30s/1min) with 0.5% profit targets.

### **Key Objectives:**
- Achieve 85%+ signal confidence consistently
- Maintain <20ms processing performance
- Ensure all Phase 1 indicators contribute to signal generation
- Eliminate dependency injection and logger casting issues
- Provide seamless ATAS platform integration
- Target 87-92% win rate improvement for scalping operations

---

## 🎯 **PRODUCT VISION**

Create the most reliable and sophisticated scalping strategy for ATAS platform that combines traditional technical analysis (Phase 1) with advanced order flow detection (Phase 2) to identify high-probability trading opportunities in ultra-low timeframe environments.

### **Success Metrics:**
- **Signal Quality:** 85%+ confidence threshold achievement
- **Performance:** <20ms total processing time
- **Reliability:** 99.9% uptime without crashes
- **Profitability:** 87-92% win rate on 0.5% profit targets
- **User Experience:** Zero configuration errors, clear diagnostics

---

## 🏗️ **ARCHITECTURE OVERVIEW**

### **Design Principles:**
1. **ATAS-Native Design:** Built specifically for ATAS platform constraints
2. **Incremental Complexity:** Start simple, add sophistication gradually
3. **Fail-Safe Operations:** Graceful degradation when components fail
4. **Performance-First:** <20ms processing built into every component
5. **Maintainable Code:** Clear separation of concerns, testable components

### **Core Components:**

#### **1. IndicatorManager (Phase 1)**
- Manages 5 traditional technical indicators
- Provides weighted signal aggregation
- Handles indicator lifecycle and validation
- Performance monitoring and optimization

#### **2. PressureDetectionEngine (Phase 2)**
- Advanced order flow analysis
- Real-time market pressure detection
- Multi-timeframe pressure correlation
- Liquidity analysis and fragmentation detection

#### **3. SignalCoordinator**
- Combines Phase 1 and Phase 2 signals
- Implements weighted voting system
- Enforces quality gates and alignment requirements
- Generates final trading signals

#### **4. AdaptiveSystem**
- Real-time parameter optimization
- Market regime detection
- Performance feedback integration
- Dynamic threshold adjustment

#### **5. OrderManager**
- Position sizing and risk management
- TP/SL order automation
- Order lifecycle tracking
- Exchange integration

---

## 📊 **PHASE 1 INDICATORS SPECIFICATION**

### **1. Volume Flow Index (VFI)**
**Purpose:** Primary volume-price relationship analysis  
**Weight:** 30% (Highest priority)  
**Parameters:**
- Period: 14 (configurable 5-50)
- Buy Threshold: 1.3 (configurable 0.5-3.0)
- Sell Threshold: 0.7 (configurable 0.1-1.5)

**UI Settings:**
```
[VFI Settings]
Period: [14] (5-50)
Buy Threshold: [1.3] (0.5-3.0)
Sell Threshold: [0.7] (0.1-1.5)
Enabled: [✓]
Weight: [30%] (10-50%)
```

### **2. Ultra-Fast RSI**
**Purpose:** Momentum analysis optimized for scalping  
**Weight:** 20%  
**Parameters:**
- Period: 3 (configurable 2-14)
- Overbought: 80 (configurable 70-90)
- Oversold: 20 (configurable 10-30)

**UI Settings:**
```
[Ultra-Fast RSI Settings]
Period: [3] (2-14)
Overbought Level: [80] (70-90)
Oversold Level: [20] (10-30)
Enabled: [✓]
Weight: [20%] (10-40%)
```

### **3. Bollinger Bands**
**Purpose:** Price volatility and mean reversion analysis  
**Weight:** 25%  
**Parameters:**
- Period: 20 (configurable 10-50)
- Standard Deviations: 2.0 (configurable 1.0-3.0)
- Price Source: Close (configurable)

**UI Settings:**
```
[Bollinger Bands Settings]
Period: [20] (10-50)
Std Deviations: [2.0] (1.0-3.0)
Price Source: [Close] (Open/High/Low/Close)
Enabled: [✓]
Weight: [25%] (10-40%)
```

### **4. Stochastic Oscillator**
**Purpose:** Price momentum and reversal detection  
**Weight:** 25%  
**Parameters:**
- %K Period: 14 (configurable 5-30)
- %D Period: 3 (configurable 1-10)
- Overbought: 80 (configurable 70-90)
- Oversold: 20 (configurable 10-30)

**UI Settings:**
```
[Stochastic Settings]
%K Period: [14] (5-30)
%D Period: [3] (1-10)
Overbought: [80] (70-90)
Oversold: [20] (10-30)
Enabled: [✓]
Weight: [25%] (10-40%)
```

### **5. MACD (Moving Average Convergence Divergence)**
**Purpose:** Trend following and momentum confirmation  
**Weight:** 20%  
**Parameters:**
- Fast Period: 12 (configurable 5-20)
- Slow Period: 26 (configurable 15-50)
- Signal Period: 9 (configurable 3-15)

**UI Settings:**
```
[MACD Settings]
Fast Period: [12] (5-20)
Slow Period: [26] (15-50)
Signal Period: [9] (3-15)
Enabled: [✓]
Weight: [20%] (10-40%)
```

---

## 🔍 **PHASE 2 PRESSURE DETECTION SPECIFICATION**

### **Phase 2A Detectors (Basic)**
1. **Print Size Clustering:** Detects unusual order size patterns
2. **Pressure Velocity Tracking:** Monitors rate of pressure changes
3. **Multi-Timeframe Delta Alignment:** Correlates pressure across timeframes
4. **Volume Rate of Change:** Identifies volume acceleration
5. **Liquidity Vacuum Detection:** Spots liquidity gaps

### **Phase 2B Detectors (Advanced)**
1. **Iceberg Order Detection:** Identifies hidden large orders
2. **Spoofing Detection:** Detects fake order placement
3. **Block Trade Identification:** Spots institutional trading
4. **Pressure Z-Score Analysis:** Statistical pressure analysis
5. **Volume Clustering Analysis:** Advanced volume pattern recognition
6. **Liquidity Fragmentation Analysis:** Order book depth analysis

---

## ⚙️ **USER INTERFACE SPECIFICATIONS**

### **Main Strategy Settings Panel**
```
[High-Probability Scalping Strategy v2.0]

=== GENERAL SETTINGS ===
Strategy Enabled: [✓]
Min Confidence Threshold: [85%] (60-95%)
Max Positions: [1] (1-5)
Position Size (USDT): [1000] (100-10000)

=== RISK MANAGEMENT ===
Stop Loss %: [0.35%] (0.1-2.0%)
Take Profit %: [0.5%] (0.2-3.0%)
Max Daily Trades: [50] (1-200)
Max Daily Loss: [5%] (1-20%)

=== SIGNAL COORDINATION ===
Min Indicator Alignment: [3] (2-5)
Phase 1 Weight: [40%] (20-80%)
Phase 2 Weight: [60%] (20-80%)
Emergency Fallback: [✓]

=== ADAPTIVE SYSTEM ===
Auto Adaptation: [✓]
Aggressiveness Level: [3] (1-5)
Learning Sensitivity: [0.5] (0.1-1.0)
Min Win Rate Threshold: [60%] (40-80%)

=== PERFORMANCE ===
Max Processing Time: [20ms] (10-50ms)
Performance Monitoring: [✓]
Debug Logging: [✓]
```

### **Phase 1 Indicators Panel**
```
[Phase 1 - Traditional Indicators]

[VFI Settings] [Expand ▼]
[Ultra-Fast RSI Settings] [Expand ▼]
[Bollinger Bands Settings] [Expand ▼]
[Stochastic Settings] [Expand ▼]
[MACD Settings] [Expand ▼]

Total Phase 1 Weight: [100%]
Active Indicators: [5/5]
Status: [✓ All Operational]
```

### **Phase 2 Pressure Detection Panel**
```
[Phase 2 - Pressure Detection]

=== PHASE 2A (BASIC) ===
Print Size Clustering: [✓] Weight: [15%]
Pressure Velocity: [✓] Weight: [20%]
Delta Alignment: [✓] Weight: [25%]
Volume Rate Change: [✓] Weight: [20%]
Liquidity Vacuum: [✓] Weight: [20%]

=== PHASE 2B (ADVANCED) ===
Iceberg Detection: [✓] Weight: [20%]
Spoofing Detection: [✓] Weight: [15%]
Block Trade ID: [✓] Weight: [25%]
Pressure Z-Score: [✓] Weight: [20%]
Volume Clustering: [✓] Weight: [20%]

Total Phase 2 Weight: [100%]
Active Detectors: [10/10]
Status: [✓ All Operational]
```

---

## 📈 **PERFORMANCE REQUIREMENTS**

### **Processing Performance**
- **Total Processing Time:** <20ms per calculation
- **Individual Indicator Time:** <2ms per indicator
- **Signal Coordination Time:** <5ms
- **Memory Usage:** <100MB total
- **CPU Usage:** <10% on modern systems

### **Signal Quality Requirements**
- **Minimum Confidence:** 85% for trade execution
- **Indicator Alignment:** Minimum 3 out of 5 indicators agreeing
- **Signal Frequency:** 5-20 signals per hour in active markets
- **False Signal Rate:** <15% (target <10%)

### **Reliability Requirements**
- **Uptime:** 99.9% without crashes
- **Error Recovery:** Automatic recovery from component failures
- **Data Validation:** 100% market data validation
- **Fail-Safe Operation:** Graceful degradation when components fail

---

## 🔧 **TECHNICAL REQUIREMENTS**

### **Platform Compatibility**
- **ATAS Version:** 5.0+ required
- **.NET Framework:** .NET 8.0
- **Windows Version:** Windows 10+ (64-bit)
- **Memory:** 8GB RAM minimum, 16GB recommended
- **Storage:** 1GB free space for logs and data

### **Dependencies**
- **No External DI Containers:** Avoid Microsoft.Extensions.DependencyInjection
- **ATAS Native APIs:** Use ATAS ChartStrategy base class
- **Simple Logging:** File-based logging with rotation
- **Performance Monitoring:** Built-in timing and metrics

### **Data Requirements**
- **Market Data:** Real-time tick data required
- **Historical Data:** Minimum 1000 bars for indicator initialization
- **Order Flow Data:** Level 2 market depth preferred
- **Latency:** <50ms market data latency

---

## 🎯 **SUCCESS CRITERIA**

### **Phase 1 Success Criteria**
- ✅ Single indicator (VFI) working without errors
- ✅ Signal generation with >70% confidence
- ✅ Performance <5ms per calculation
- ✅ Clear ATAS integration

### **Phase 2 Success Criteria**
- ✅ All 5 Phase 1 indicators operational
- ✅ Signal logs show "Phase1: 3-5" contributions
- ✅ Combined confidence >80%
- ✅ Performance <15ms total

### **Phase 3 Success Criteria**
- ✅ Signal coordination working properly
- ✅ Consistent 85%+ confidence signals
- ✅ Proper indicator alignment (3+ indicators)
- ✅ No emergency fallback signals

### **Final Success Criteria**
- ✅ Complete Phase 1 + Phase 2 integration
- ✅ Adaptive system operational
- ✅ Order management functional
- ✅ 87-92% win rate achievement
- ✅ <20ms total processing time

---

## 📅 **PROJECT TIMELINE**

### **Phase 1: Foundation** (Week 1)
- Basic ATAS strategy framework
- Single indicator implementation (VFI)
- Performance monitoring setup
- Basic logging infrastructure

### **Phase 2: Multi-Indicator** (Week 2)
- Remaining 4 Phase 1 indicators
- IndicatorManager implementation
- Signal aggregation system
- UI settings integration

### **Phase 3: Signal Coordination** (Week 3)
- Weighted voting system
- Quality gates implementation
- Alignment requirements
- Confidence thresholds

### **Phase 4: Phase 2 Integration** (Week 4)
- Pressure detection engine
- Phase 1 + Phase 2 coordination
- Advanced signal generation
- Performance optimization

### **Phase 5: Adaptive System** (Week 5)
- Parameter optimization
- Market regime detection
- Performance feedback loops
- Dynamic adjustments

### **Phase 6: Trading Integration** (Week 6)
- Order management system
- Position tracking
- TP/SL automation
- Risk management

---

## 🔍 **RISK ASSESSMENT**

### **Technical Risks**
- **ATAS API Changes:** Medium risk - mitigate with version compatibility checks
- **Performance Degradation:** Low risk - built-in monitoring and optimization
- **Market Data Issues:** Medium risk - implement data validation and fallbacks

### **Business Risks**
- **Market Conditions:** High risk - adaptive system designed to handle various conditions
- **Regulatory Changes:** Low risk - strategy operates within standard trading parameters
- **Competition:** Medium risk - focus on unique Phase 2 pressure detection

### **Mitigation Strategies**
- Comprehensive testing at each phase
- Performance monitoring and alerting
- Graceful degradation mechanisms
- Regular market condition validation

---

## 📊 **MONITORING AND ANALYTICS**

### **Real-Time Monitoring**
- Signal generation frequency and quality
- Processing time per component
- Memory and CPU usage
- Error rates and recovery times

### **Performance Analytics**
- Win/loss ratios by market conditions
- Indicator contribution analysis
- Signal confidence distribution
- Adaptive system effectiveness

### **Reporting**
- Daily performance summaries
- Weekly strategy optimization reports
- Monthly market condition analysis
- Quarterly strategy evolution reports

---

This PRD serves as the foundation for the High-Probability Scalping Strategy v2.0 rebuild project. Each phase will have detailed implementation specifications in separate phase documents.
